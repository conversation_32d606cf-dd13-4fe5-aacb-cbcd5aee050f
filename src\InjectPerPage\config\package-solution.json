{"$schema": "https://developer.microsoft.com/json-schemas/spfx-build/package-solution.schema.json", "solution": {"name": "script-injector-client-side-solution", "id": "89312a29-da49-4222-a379-430d5b476f2d", "version": "*******", "includeClientSideAssets": true, "skipFeatureDeployment": true, "isDomainIsolated": false, "developer": {"name": "", "websiteUrl": "", "privacyUrl": "", "termsOfUseUrl": "", "mpnId": "Undefined-1.21.1"}, "metadata": {"shortDescription": {"default": "ScriptInjector description"}, "longDescription": {"default": "ScriptInjector description"}, "screenshotPaths": [], "videoUrl": "", "categories": []}, "features": [{"title": "script-injector Feature", "description": "The feature that activates elements of the script-injector solution.", "id": "0b384bb3-3146-47ce-b6cb-0a92e86c79cc", "version": "*******"}]}, "paths": {"zippedPackage": "solution/script-injector.sppkg"}}