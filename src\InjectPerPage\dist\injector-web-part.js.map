{"version": 3, "file": "injector-web-part.js", "mappings": ";;;;;;;;;;;;;;;AAAA;AACqJ;;AAErJ,wIAAiC,qBAAqB,6CAA6C,sBAAsB,gBAAgB,YAAY,kCAAkC,uFAAuF,kBAAkB,kBAAkB,uBAAuB,gBAAgB,WAAW,kBAAkB,wCAAwC,kBAAkB,qBAAqB,wBAAwB,gDAAgD,yBAAyB,0BAA0B,6CAA6C;;AAE7mB;AACA,iEAAe;AACf;AACA;AACA;AACA;AACA;AACA,CAAC,EAAC;;;;;;;;;;;;;;;;ACXF,mBAAO,CAAC,uCAA8B,CAAC,CAAC;AACxC,IAAM,MAAM,GAAG;IACb,QAAQ,EAAE,mBAAmB;IAC7B,KAAK,EAAE,gBAAgB;IACvB,OAAO,EAAE,kBAAkB;IAC3B,YAAY,EAAE,uBAAuB;IACrC,KAAK,EAAE,gBAAgB;CACxB,CAAC;AAEF,iEAAe,MAAM,EAAC;;;;;;;;;;;ACVtB,mD;;;;;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA,gBAAgB,SAAI,IAAI,SAAI;AAC5B;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA,CAAC,oBAAoB;AACrB;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,8CAA8C;AAC/C;AACA;AACA,4CAA4C,qBAAM,WAAW;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,YAAY;AAChD;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,eAAe;AACf;AACA;AACA,oCAAoC,YAAY,8BAA8B;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,wBAAwB;AACnC,WAAW,SAAS;AACpB;AACO;AACP,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,WAAW;AACX;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,cAAc;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oEAAoE,gBAAgB;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,eAAe;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,SAAY;AAC5B,gBAAgB,IAAK;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB;AACO;AACP;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,eAAe;AAC1B,WAAW,cAAc;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AC/TA,kD;;;;;;;;;;;ACAA,kD;;;;;;;;;;;ACAA,kD;;;;;;;;;;;ACAA,kD;;;;;;;;;;;ACAA,kD;;;;;;;;;;;ACAA,kD;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA,E;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA,E;;;;;WCPA;WACA;WACA;WACA;WACA,GAAG;WACH;WACA;WACA,CAAC,I;;;;;WCPD,wF;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D,E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACNqD;AAIhB;AAC8B;AAEV;AACJ;AAEF;AACD;AACpB;AACF;AAQ5B;IAA6C,mCAA4C;IAAzF;;QAEU,yBAAmB,GAAW,EAAE,CAAC;;IA0F3C,CAAC;IAzFC,yCAAyC;IAEzC,sDAAsD;IACtC,gCAAM,GAAtB;;;;;;4BACE,qBAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC,IAAI,CAAC,WAAC,IAAI,YAAI,CAAC,mBAAmB,GAAG,CAAC,EAA5B,CAA4B,CAAC;;wBAA3E,SAA2E,CAAC;wBAE5E,kCAAkC;wBAClC,IAAI,UAAI,CAAC,UAAU,0CAAE,MAAM,EAAE,CAAC;4BAC5B,mEAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;wBACpD,CAAC;6BAEG,WAAI,CAAC,UAAU,0CAAE,KAAK,GAAtB,wBAAsB;wBACxB,qBAAM,mEAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;wBAAzD,SAAyD,CAAC;;;;;;KAE7D;IAEM,gCAAM,GAAb;QACE,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,mCACR,oEAAM,CAAC,QAAQ,cAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,oEAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,uCAC3E,oEAAM,CAAC,OAAO,oCAChB,mEAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,gDACpD,IAAI,CAAC,mBAAmB,qDACP,mEAAM,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,QAAQ,CAAC,6DAC3C,mEAAM,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI,QAAQ,CAAC,uIAGzD,CAAC;IAChB,CAAC;IAEO,gDAAsB,GAA9B;QAAA,iBAuBC;QAtBC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACvC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;iBAC7D,IAAI,CAAC,iBAAO;gBACX,IAAI,kBAAkB,GAAW,EAAE,CAAC;gBACpC,QAAQ,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;oBAC9B,KAAK,QAAQ;wBACX,kBAAkB,GAAG,KAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,6EAAiC,CAAC,CAAC,CAAC,wEAA4B,CAAC;wBAC3H,MAAM;oBACR,KAAK,SAAS;wBACZ,kBAAkB,GAAG,KAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,8EAAkC,CAAC,CAAC,CAAC,yEAA6B,CAAC;wBAC7H,MAAM;oBACR,KAAK,OAAO,CAAC;oBACb,KAAK,aAAa;wBAChB,kBAAkB,GAAG,KAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,4EAAgC,CAAC,CAAC,CAAC,0EAA8B,CAAC;wBAC5H,MAAM;oBACR;wBACE,kBAAkB,GAAG,sEAA0B,CAAC;gBACpD,CAAC;gBACD,OAAO,kBAAkB,CAAC;YAC5B,CAAC,CAAC,CAAC;QACP,CAAC;QACD,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,iFAAqC,CAAC,CAAC,CAAC,4EAAgC,CAAC,CAAC;IACxI,CAAC;IAES,wCAAc,GAAxB,UAAyB,YAAwC;QAC/D,IAAI,CAAC,YAAY;YAAE,OAAO;QAC1B,iDAAiD;QACzC,kBAAc,GAAK,YAAY,eAAjB,CAAkB;QACxC,IAAI,cAAc,EAAE,CAAC;YACnB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,YAAY,EAAE,cAAc,CAAC,QAAQ,IAAI,IAAI,CAAC,CAAC;YACjF,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,EAAE,cAAc,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC;YACzE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,eAAe,EAAE,cAAc,CAAC,WAAW,IAAI,IAAI,CAAC,CAAC;QACzF,CAAC;IACH,CAAC;IAED,sBAAc,wCAAW;aAAzB;YACE,OAAO,+DAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC;;;OAAA;IAED,mEAAmE;IACzD,sDAA4B,GAAtC;QACE,OAAO;YACL,KAAK,EAAE;gBACL;oBACE,MAAM,EAAE,EAAE,WAAW,EAAE,2EAA+B,EAAE;oBACxD,MAAM,EAAE;wBACN;4BACE,SAAS,EAAE,kEAAsB;4BACjC,WAAW,EAAE;gCACX,kFAAqB,CAAC,aAAa,EAAE,EAAE,KAAK,EAAE,yEAA6B,EAAE,CAAC;gCAC9E,kFAAqB,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,oBAAoB,EAAE,CAAC;gCAChE,kFAAqB,CAAC,OAAO,EAAG,EAAE,KAAK,EAAE,mBAAmB,EAAE,CAAC;6BAChE;yBACF;qBACF;iBACF;aACF;SACF,CAAC;IACJ,CAAC;IACH,sBAAC;AAAD,CAAC,CA5F4C,6EAAqB,GA4FjE", "sources": ["webpack:///.././lib/webparts/injector/InjectorWebPart.module.css", "webpack:///.././src/webparts/injector/InjectorWebPart.module.scss.ts", "webpack:///.././lib/webparts/injector/assets/custom.js", "webpack:///.././node_modules/@microsoft/sp-css-loader/node_modules/@microsoft/load-themed-styles/lib-es6/index.js", "webpack:///../external amd \"@microsoft/sp-core-library\"", "webpack:///../external amd \"@microsoft/sp-loader\"", "webpack:///../external amd \"@microsoft/sp-lodash-subset\"", "webpack:///../external amd \"@microsoft/sp-property-pane\"", "webpack:///../external amd \"@microsoft/sp-webpart-base\"", "webpack:///../external amd \"InjectorWebPartStrings\"", "webpack:///../webpack/bootstrap", "webpack:///../webpack/runtime/compat get default export", "webpack:///../webpack/runtime/define property getters", "webpack:///../webpack/runtime/global", "webpack:///../webpack/runtime/hasOwnProperty shorthand", "webpack:///../webpack/runtime/make namespace object", "webpack:///.././src/webparts/injector/InjectorWebPart.ts"], "sourcesContent": ["// Imports\nimport * as __LOAD_THEMED_STYLES__ from \"../../../node_modules/@microsoft/sp-css-loader/node_modules/@microsoft/load-themed-styles/lib-es6/index.js\";\n\n__LOAD_THEMED_STYLES__.loadStyles(\".injector_b6c00275{color:\\\"[theme:bodyText, default: #323130]\\\";color:var(--bodyText);overflow:hidden;padding:1em}.injector_b6c00275.teams_b6c00275{font-family:Segoe UI,-apple-system,BlinkMacSystemFont,Roboto,Helvetica Neue,sans-serif}.welcome_b6c00275{text-align:center}.welcomeImage_b6c00275{max-width:420px;width:100%}.links_b6c00275 a{color:\\\"[theme:link, default:#03787c]\\\";color:var(--link);text-decoration:none}.links_b6c00275 a:hover{color:\\\"[theme:linkHovered, default: #014446]\\\";color:var(--linkHovered);text-decoration:underline}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImZpbGU6Ly8vQzovc3JjL3NlbmNvcnAvc3JjL0luamVjdFBlclBhZ2Uvc3JjL3dlYnBhcnRzL2luamVjdG9yL0luamVjdG9yV2ViUGFydC5tb2R1bGUuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFFQSxtQkFHRSwwQ0FBQSxDQUNBLHFCQUFBLENBSEEsZUFBQSxDQUNBLFdBRUEsQ0FDQSxrQ0FDRSxzRkFBQSxDQUlKLGtCQUNFLGlCQUFBLENBR0YsdUJBRUUsZUFBQSxDQURBLFVBQ0EsQ0FJQSxrQkFFRSxxQ0FBQSxDQUNBLGlCQUFBLENBRkEsb0JBRUEsQ0FFQSx3QkFFRSw2Q0FBQSxDQUNBLHdCQUFBLENBRkEseUJBRUEiLCJmaWxlIjoiSW5qZWN0b3JXZWJQYXJ0Lm1vZHVsZS5jc3MifQ== */\", true);\n\n// Exports\nexport default {\n  injector_b6c00275: \"injector_b6c00275\",\n  teams_b6c00275: \"teams_b6c00275\",\n  welcome_b6c00275: \"welcome_b6c00275\",\n  welcomeImage_b6c00275: \"welcomeImage_b6c00275\",\n  links_b6c00275: \"links_b6c00275\"\n};\n", "\r\nrequire(\"./InjectorWebPart.module.css\");\r\nconst styles = {\r\n  injector: 'injector_b6c00275',\r\n  teams: 'teams_b6c00275',\r\n  welcome: 'welcome_b6c00275',\r\n  welcomeImage: 'welcomeImage_b6c00275',\r\n  links: 'links_b6c00275'\r\n};\r\n\r\nexport default styles;\r\n", "document.documentElement.classList.add('nm-theme');", "// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\n/**\n * In sync mode, styles are registered as style elements synchronously with loadStyles() call.\n * In async mode, styles are buffered and registered as batch in async timer for performance purpose.\n */\nexport var Mode;\n(function (Mode) {\n    Mode[Mode[\"sync\"] = 0] = \"sync\";\n    Mode[Mode[\"async\"] = 1] = \"async\";\n})(Mode || (Mode = {}));\n/**\n * Themable styles and non-themable styles are tracked separately\n * Specify ClearStyleOptions when calling clearStyles API to specify which group of registered styles should be cleared.\n */\nexport var ClearStyleOptions;\n(function (ClearStyleOptions) {\n    /** only themable styles will be cleared */\n    ClearStyleOptions[ClearStyleOptions[\"onlyThemable\"] = 1] = \"onlyThemable\";\n    /** only non-themable styles will be cleared */\n    ClearStyleOptions[ClearStyleOptions[\"onlyNonThemable\"] = 2] = \"onlyNonThemable\";\n    /** both themable and non-themable styles will be cleared */\n    ClearStyleOptions[ClearStyleOptions[\"all\"] = 3] = \"all\";\n})(ClearStyleOptions || (ClearStyleOptions = {}));\n// Store the theming state in __themeState__ global scope for reuse in the case of duplicate\n// load-themed-styles hosted on the page.\nvar _root = typeof window === 'undefined' ? global : window; // eslint-disable-line @typescript-eslint/no-explicit-any\n// Nonce string to inject into script tag if one provided. This is used in CSP (Content Security Policy).\nvar _styleNonce = _root && _root.CSPSettings && _root.CSPSettings.nonce;\nvar _themeState = initializeThemeState();\n/**\n * Matches theming tokens. For example, \"[theme: themeSlotName, default: #FFF]\" (including the quotes).\n */\nvar _themeTokenRegex = /[\\'\\\"]\\[theme:\\s*(\\w+)\\s*(?:\\,\\s*default:\\s*([\\\\\"\\']?[\\.\\,\\(\\)\\#\\-\\s\\w]*[\\.\\,\\(\\)\\#\\-\\w][\\\"\\']?))?\\s*\\][\\'\\\"]/g;\nvar now = function () {\n    return typeof performance !== 'undefined' && !!performance.now ? performance.now() : Date.now();\n};\nfunction measure(func) {\n    var start = now();\n    func();\n    var end = now();\n    _themeState.perf.duration += end - start;\n}\n/**\n * initialize global state object\n */\nfunction initializeThemeState() {\n    var state = _root.__themeState__ || {\n        theme: undefined,\n        lastStyleElement: undefined,\n        registeredStyles: []\n    };\n    if (!state.runState) {\n        state = __assign(__assign({}, state), { perf: {\n                count: 0,\n                duration: 0\n            }, runState: {\n                flushTimer: 0,\n                mode: Mode.sync,\n                buffer: []\n            } });\n    }\n    if (!state.registeredThemableStyles) {\n        state = __assign(__assign({}, state), { registeredThemableStyles: [] });\n    }\n    _root.__themeState__ = state;\n    return state;\n}\n/**\n * Loads a set of style text. If it is registered too early, we will register it when the window.load\n * event is fired.\n * @param {string | ThemableArray} styles Themable style text to register.\n * @param {boolean} loadAsync When true, always load styles in async mode, irrespective of current sync mode.\n */\nexport function loadStyles(styles, loadAsync) {\n    if (loadAsync === void 0) { loadAsync = false; }\n    measure(function () {\n        var styleParts = Array.isArray(styles) ? styles : splitStyles(styles);\n        var _a = _themeState.runState, mode = _a.mode, buffer = _a.buffer, flushTimer = _a.flushTimer;\n        if (loadAsync || mode === Mode.async) {\n            buffer.push(styleParts);\n            if (!flushTimer) {\n                _themeState.runState.flushTimer = asyncLoadStyles();\n            }\n        }\n        else {\n            applyThemableStyles(styleParts);\n        }\n    });\n}\n/**\n * Allows for customizable loadStyles logic. e.g. for server side rendering application\n * @param {(processedStyles: string, rawStyles?: string | ThemableArray) => void}\n * a loadStyles callback that gets called when styles are loaded or reloaded\n */\nexport function configureLoadStyles(loadStylesFn) {\n    _themeState.loadStyles = loadStylesFn;\n}\n/**\n * Configure run mode of load-themable-styles\n * @param mode load-themable-styles run mode, async or sync\n */\nexport function configureRunMode(mode) {\n    _themeState.runState.mode = mode;\n}\n/**\n * external code can call flush to synchronously force processing of currently buffered styles\n */\nexport function flush() {\n    measure(function () {\n        var styleArrays = _themeState.runState.buffer.slice();\n        _themeState.runState.buffer = [];\n        var mergedStyleArray = [].concat.apply([], styleArrays);\n        if (mergedStyleArray.length > 0) {\n            applyThemableStyles(mergedStyleArray);\n        }\n    });\n}\n/**\n * register async loadStyles\n */\nfunction asyncLoadStyles() {\n    // Use \"self\" to distinguish conflicting global typings for setTimeout() from lib.dom.d.ts vs Jest's @types/node\n    // https://github.com/jestjs/jest/issues/14418\n    return self.setTimeout(function () {\n        _themeState.runState.flushTimer = 0;\n        flush();\n    }, 0);\n}\n/**\n * Loads a set of style text. If it is registered too early, we will register it when the window.load event\n * is fired.\n * @param {string} styleText Style to register.\n * @param {IStyleRecord} styleRecord Existing style record to re-apply.\n */\nfunction applyThemableStyles(stylesArray, styleRecord) {\n    if (_themeState.loadStyles) {\n        _themeState.loadStyles(resolveThemableArray(stylesArray).styleString, stylesArray);\n    }\n    else {\n        registerStyles(stylesArray);\n    }\n}\n/**\n * Registers a set theme tokens to find and replace. If styles were already registered, they will be\n * replaced.\n * @param {theme} theme JSON object of theme tokens to values.\n */\nexport function loadTheme(theme) {\n    _themeState.theme = theme;\n    // reload styles.\n    reloadStyles();\n}\n/**\n * Clear already registered style elements and style records in theme_State object\n * @param option - specify which group of registered styles should be cleared.\n * Default to be both themable and non-themable styles will be cleared\n */\nexport function clearStyles(option) {\n    if (option === void 0) { option = ClearStyleOptions.all; }\n    if (option === ClearStyleOptions.all || option === ClearStyleOptions.onlyNonThemable) {\n        clearStylesInternal(_themeState.registeredStyles);\n        _themeState.registeredStyles = [];\n    }\n    if (option === ClearStyleOptions.all || option === ClearStyleOptions.onlyThemable) {\n        clearStylesInternal(_themeState.registeredThemableStyles);\n        _themeState.registeredThemableStyles = [];\n    }\n}\nfunction clearStylesInternal(records) {\n    records.forEach(function (styleRecord) {\n        var styleElement = styleRecord && styleRecord.styleElement;\n        if (styleElement && styleElement.parentElement) {\n            styleElement.parentElement.removeChild(styleElement);\n        }\n    });\n}\n/**\n * Reloads styles.\n */\nfunction reloadStyles() {\n    if (_themeState.theme) {\n        var themableStyles = [];\n        for (var _i = 0, _a = _themeState.registeredThemableStyles; _i < _a.length; _i++) {\n            var styleRecord = _a[_i];\n            themableStyles.push(styleRecord.themableStyle);\n        }\n        if (themableStyles.length > 0) {\n            clearStyles(ClearStyleOptions.onlyThemable);\n            applyThemableStyles([].concat.apply([], themableStyles));\n        }\n    }\n}\n/**\n * Find theme tokens and replaces them with provided theme values.\n * @param {string} styles Tokenized styles to fix.\n */\nexport function detokenize(styles) {\n    if (styles) {\n        styles = resolveThemableArray(splitStyles(styles)).styleString;\n    }\n    return styles;\n}\n/**\n * Resolves ThemingInstruction objects in an array and joins the result into a string.\n * @param {ThemableArray} splitStyleArray ThemableArray to resolve and join.\n */\nfunction resolveThemableArray(splitStyleArray) {\n    var theme = _themeState.theme;\n    var themable = false;\n    // Resolve the array of theming instructions to an array of strings.\n    // Then join the array to produce the final CSS string.\n    var resolvedArray = (splitStyleArray || []).map(function (currentValue) {\n        var themeSlot = currentValue.theme;\n        if (themeSlot) {\n            themable = true;\n            // A theming annotation. Resolve it.\n            var themedValue = theme ? theme[themeSlot] : undefined;\n            var defaultValue = currentValue.defaultValue || 'inherit';\n            // Warn to console if we hit an unthemed value even when themes are provided, but only if \"DEBUG\" is true.\n            // Allow the themedValue to be undefined to explicitly request the default value.\n            if (theme &&\n                !themedValue &&\n                console &&\n                !(themeSlot in theme) &&\n                typeof DEBUG !== 'undefined' &&\n                DEBUG) {\n                // eslint-disable-next-line no-console\n                console.warn(\"Theming value not provided for \\\"\".concat(themeSlot, \"\\\". Falling back to \\\"\").concat(defaultValue, \"\\\".\"));\n            }\n            return themedValue || defaultValue;\n        }\n        else {\n            // A non-themable string. Preserve it.\n            return currentValue.rawString;\n        }\n    });\n    return {\n        styleString: resolvedArray.join(''),\n        themable: themable\n    };\n}\n/**\n * Split tokenized CSS into an array of strings and theme specification objects\n * @param {string} styles Tokenized styles to split.\n */\nexport function splitStyles(styles) {\n    var result = [];\n    if (styles) {\n        var pos = 0; // Current position in styles.\n        var tokenMatch = void 0;\n        while ((tokenMatch = _themeTokenRegex.exec(styles))) {\n            var matchIndex = tokenMatch.index;\n            if (matchIndex > pos) {\n                result.push({\n                    rawString: styles.substring(pos, matchIndex)\n                });\n            }\n            result.push({\n                theme: tokenMatch[1],\n                defaultValue: tokenMatch[2] // May be undefined\n            });\n            // index of the first character after the current match\n            pos = _themeTokenRegex.lastIndex;\n        }\n        // Push the rest of the string after the last match.\n        result.push({\n            rawString: styles.substring(pos)\n        });\n    }\n    return result;\n}\n/**\n * Registers a set of style text. If it is registered too early, we will register it when the\n * window.load event is fired.\n * @param {ThemableArray} styleArray Array of IThemingInstruction objects to register.\n * @param {IStyleRecord} styleRecord May specify a style Element to update.\n */\nfunction registerStyles(styleArray) {\n    if (typeof document === 'undefined') {\n        return;\n    }\n    var head = document.getElementsByTagName('head')[0];\n    var styleElement = document.createElement('style');\n    var _a = resolveThemableArray(styleArray), styleString = _a.styleString, themable = _a.themable;\n    styleElement.setAttribute('data-load-themed-styles', 'true');\n    if (_styleNonce) {\n        styleElement.setAttribute('nonce', _styleNonce);\n    }\n    styleElement.appendChild(document.createTextNode(styleString));\n    _themeState.perf.count++;\n    head.appendChild(styleElement);\n    var ev = document.createEvent('HTMLEvents');\n    ev.initEvent('styleinsert', true /* bubbleEvent */, false /* cancelable */);\n    ev.args = {\n        newStyle: styleElement\n    };\n    document.dispatchEvent(ev);\n    var record = {\n        styleElement: styleElement,\n        themableStyle: styleArray\n    };\n    if (themable) {\n        _themeState.registeredThemableStyles.push(record);\n    }\n    else {\n        _themeState.registeredStyles.push(record);\n    }\n}\n", "module.exports = __WEBPACK_EXTERNAL_MODULE__676__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__414__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__529__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__877__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__642__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__243__;", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import { Version } from '@microsoft/sp-core-library';\nimport {\n  type IPropertyPaneConfiguration,\n  PropertyPaneTextField\n} from '@microsoft/sp-property-pane';\nimport { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';\nimport type { IReadonlyTheme } from '@microsoft/sp-component-base';\nimport { SPComponentLoader } from '@microsoft/sp-loader';\nimport { escape } from '@microsoft/sp-lodash-subset';\n\nimport styles from './InjectorWebPart.module.scss';\nimport * as strings from 'InjectorWebPartStrings';\nimport './assets/custom.scss';\nimport './assets/custom.js';\n\nexport interface IInjectorWebPartProps {\n  description: string;\n  cssUrl?: string;   \n  jsUrl?: string;  \n}\n\nexport default class InjectorWebPart extends BaseClientSideWebPart<IInjectorWebPartProps> {\n\n  private _environmentMessage: string = '';\n  // private _isDarkTheme: boolean = false;\n\n  /** Load CSS/JS as soon as the web part initializes */\n  protected async onInit(): Promise<void> {\n    await this._getEnvironmentMessage().then(m => this._environmentMessage = m);\n\n    // Inject CSS first (non-blocking)\n    if (this.properties?.cssUrl) {\n      SPComponentLoader.loadCss(this.properties.cssUrl);\n    }\n    // Then inject JS (await so you can rely on it being present after init)\n    if (this.properties?.jsUrl) {\n      await SPComponentLoader.loadScript(this.properties.jsUrl);\n    }\n  }\n\n  public render(): void {\n    this.domElement.innerHTML = `\n      <section class=\"${styles.injector} ${!!this.context.sdks.microsoftTeams ? styles.teams : ''}\">\n        <div class=\"${styles.welcome}\">\n          <h2>Hi, ${escape(this.context.pageContext.user.displayName)} 👋</h2>\n          <div>${this._environmentMessage}</div>\n          <div>CSS URL: <strong>${escape(this.properties.cssUrl || '(none)')}</strong></div>\n          <div>JS URL: <strong>${escape(this.properties.jsUrl || '(none)')}</strong></div>\n          <div>Note: URLs are configurable in the web part property pane.</div>\n        </div>\n      </section>`;\n  }\n\n  private _getEnvironmentMessage(): Promise<string> {\n    if (!!this.context.sdks.microsoftTeams) {\n      return this.context.sdks.microsoftTeams.teamsJs.app.getContext()\n        .then(context => {\n          let environmentMessage: string = '';\n          switch (context.app.host.name) {\n            case 'Office':\n              environmentMessage = this.context.isServedFromLocalhost ? strings.AppLocalEnvironmentOffice : strings.AppOfficeEnvironment;\n              break;\n            case 'Outlook':\n              environmentMessage = this.context.isServedFromLocalhost ? strings.AppLocalEnvironmentOutlook : strings.AppOutlookEnvironment;\n              break;\n            case 'Teams':\n            case 'TeamsModern':\n              environmentMessage = this.context.isServedFromLocalhost ? strings.AppLocalEnvironmentTeams : strings.AppTeamsTabEnvironment;\n              break;\n            default:\n              environmentMessage = strings.UnknownEnvironment;\n          }\n          return environmentMessage;\n        });\n    }\n    return Promise.resolve(this.context.isServedFromLocalhost ? strings.AppLocalEnvironmentSharePoint : strings.AppSharePointEnvironment);\n  }\n\n  protected onThemeChanged(currentTheme: IReadonlyTheme | undefined): void {\n    if (!currentTheme) return;\n    // this._isDarkTheme = !!currentTheme.isInverted;\n    const { semanticColors } = currentTheme;\n    if (semanticColors) {\n      this.domElement.style.setProperty('--bodyText', semanticColors.bodyText || null);\n      this.domElement.style.setProperty('--link', semanticColors.link || null);\n      this.domElement.style.setProperty('--linkHovered', semanticColors.linkHovered || null);\n    }\n  }\n\n  protected get dataVersion(): Version {\n    return Version.parse('1.0');\n  }\n\n  /** Add fields for CSS/JS URLs so editors can configure per page */\n  protected getPropertyPaneConfiguration(): IPropertyPaneConfiguration {\n    return {\n      pages: [\n        {\n          header: { description: strings.PropertyPaneDescription },\n          groups: [\n            {\n              groupName: strings.BasicGroupName,\n              groupFields: [\n                PropertyPaneTextField('description', { label: strings.DescriptionFieldLabel }),\n                PropertyPaneTextField('cssUrl', { label: 'CSS URL (optional)' }),\n                PropertyPaneTextField('jsUrl',  { label: 'JS URL (optional)' })\n              ]\n            }\n          ]\n        }\n      ]\n    };\n  }\n}\n"], "names": [], "sourceRoot": ""}