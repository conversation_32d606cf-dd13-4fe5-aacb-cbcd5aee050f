import { Version } from '@microsoft/sp-core-library';
import { type IPropertyPaneConfiguration } from '@microsoft/sp-property-pane';
import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import type { IReadonlyTheme } from '@microsoft/sp-component-base';
export interface IInjectorWebPartProps {
    description: string;
    cssUrl?: string;
    jsUrl?: string;
}
export default class InjectorWebPart extends BaseClientSideWebPart<IInjectorWebPartProps> {
    private _environmentMessage;
    /** Load CSS/JS as soon as the web part initializes */
    protected onInit(): Promise<void>;
    render(): void;
    private _getEnvironmentMessage;
    protected onThemeChanged(currentTheme: IReadonlyTheme | undefined): void;
    protected get dataVersion(): Version;
    /** Add fields for CSS/JS URLs so editors can configure per page */
    protected getPropertyPaneConfiguration(): IPropertyPaneConfiguration;
}
//# sourceMappingURL=InjectorWebPart.d.ts.map