:global {
  /* --- Make top navigation link hover red --- */

  /* Modern site header (most tenants) */
  .nm-theme div[data-automationid="SiteHeader"] a[role="menuitem"]:hover,
  .nm-theme div[data-automationid="SiteHeader"] button[role="menuitem"]:hover,
  .nm-theme div[data-automationid="SiteHeader"] a[role="menuitem"]:hover span,
  .nm-theme div[data-automationid="SiteHeader"] button[role="menuitem"]:hover span {
    color: #c00000 !important;  /* red */
  }

  /* If an underline/indicator appears on hover, tint it red too */
  .nm-theme div[data-automationid="SiteHeader"] a[role="menuitem"]:hover::after,
  .nm-theme div[data-automationid="SiteHeader"] button[role="menuitem"]:hover::after {
    background-color: #c00000 !important;
  }

  /* Fallbacks for other nav renderings you may encounter */
  .nm-theme [data-automationid="TopNav"] a:hover,
  .nm-theme [data-automationid="HorizontalNav"] a:hover,
  .nm-theme .ms-HorizontalNavItem-link:hover {
    color: #c00000 !important;
  }
}