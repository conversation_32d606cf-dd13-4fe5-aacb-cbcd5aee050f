{"version": 3, "file": "GraphErrorHandler.js", "sourceRoot": "", "sources": ["../../../src/GraphErrorHandler.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;;AAEH;;GAEG;AAEH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAiB1C;;;GAGG;AAEH,MAAM,OAAO,iBAAiB;IAC7B;;;;;;;OAOG;IACK,MAAM,CAAC,cAAc,CAAC,KAAY,EAAE,UAAmB;QAC9D,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,UAAU,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;QACrD,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,EAAE;YAC7B,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;SACzB;QACD,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;QAC/B,MAAM,CAAC,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;QACzB,OAAO,MAAM,CAAC;IACf,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACK,MAAM,CAAC,0BAA0B,CAAC,UAAiC,EAAE,UAAkB;QAC9F,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC;QAC/B,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,UAAU,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QACzD,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QACzB,IAAI,KAAK,CAAC,UAAU,KAAK,SAAS,EAAE;YACnC,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;YAClD,MAAM,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;SAC9C;QAED,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAEpC,OAAO,MAAM,CAAC;IACf,CAAC;IAED;;;;;;;;;;OAUG;IACI,MAAM,CAAO,QAAQ,CAAC,QAAa,IAAI,EAAE,UAAU,GAAG,CAAC,CAAC,EAAE,QAA+B;;YAC/F,IAAI,MAAkB,CAAC;YACvB,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;gBACzB,MAAM,GAAG,iBAAiB,CAAC,0BAA0B,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;aACzE;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE;gBAClC,MAAM,GAAG,iBAAiB,CAAC,cAAc,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;aAC7D;iBAAM;gBACN,MAAM,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;gBACpC,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,4FAA4F;aACjH;YACD,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;gBACnC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;aACvB;iBAAM;gBACN,OAAO,MAAM,CAAC;aACd;QACF,CAAC;KAAA;CACD"}