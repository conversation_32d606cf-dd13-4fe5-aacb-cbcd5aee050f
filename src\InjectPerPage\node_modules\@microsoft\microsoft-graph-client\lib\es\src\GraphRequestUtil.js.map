{"version": 3, "file": "GraphRequestUtil.js", "sourceRoot": "", "sources": ["../../../src/GraphRequestUtil.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH;;GAEG;AACH,OAAO,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;AACzC,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AACtD;;GAEG;AACH,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;AAEtH;;;;GAIG;AACH,MAAM,CAAC,MAAM,OAAO,GAAG,CAAC,WAAqB,EAAU,EAAE;IACxD,MAAM,eAAe,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;IACrD,MAAM,cAAc,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;IACpD,MAAM,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACnF,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACtD,OAAO,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC7B,CAAC,CAAC;AAEF;;;;;;;;;;;GAWG;AAEH,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,OAAY,EAAO,EAAE;IACrD,MAAM,SAAS,GAAW,OAAO,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC;IACrF,IAAI,SAAS,KAAK,QAAQ,IAAI,SAAS,KAAK,MAAM,IAAI,SAAS,KAAK,MAAM,IAAI,SAAS,KAAK,UAAU,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QACtI,OAAO,OAAO,CAAC;KACf;IACD,IAAI,SAAS,KAAK,aAAa,EAAE;QAChC,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KAC/B;SAAM,IAAI,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,YAAY,IAAI,SAAS,KAAK,YAAY,IAAI,SAAS,KAAK,YAAY,IAAI,SAAS,KAAK,aAAa,IAAI,SAAS,KAAK,aAAa,IAAI,SAAS,KAAK,mBAAmB,IAAI,SAAS,KAAK,cAAc,IAAI,SAAS,KAAK,cAAc,IAAI,SAAS,KAAK,UAAU,EAAE;QAC9T,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;KACtC;SAAM;QACN,IAAI;YACH,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;SAClC;QAAC,OAAO,KAAK,EAAE;YACf,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;SACnD;KACD;IACD,OAAO,OAAO,CAAC;AAChB,CAAC,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,MAAM,UAAU,GAAG,CAAC,GAAW,EAAW,EAAE;IAClD,OAAO,eAAe,CAAC,GAAG,CAAC,CAAC;AAC7B,CAAC,CAAC;AAEF;;;;;GAKG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,GAAW,EAAE,WAAwB,EAAW,EAAE;IAC9E,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;IACvD,OAAO,eAAe,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;AAC1C,CAAC,CAAC;AAEF;;;;;GAKG;AACH,MAAM,eAAe,GAAG,CAAC,GAAW,EAAE,eAA4B,UAAU,EAAW,EAAE;IACxF,gGAAgG;IAChG,8DAA8D;IAC9D,GAAG,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;IAExB,IAAI,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;QACnC,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;QAElC,2BAA2B;QAC3B,MAAM,gBAAgB,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC1C,MAAM,eAAe,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACzC,IAAI,QAAQ,GAAG,EAAE,CAAC;QAClB,IAAI,eAAe,KAAK,CAAC,CAAC,EAAE;YAC3B,IAAI,gBAAgB,KAAK,CAAC,CAAC,IAAI,gBAAgB,GAAG,eAAe,EAAE;gBAClE,QAAQ,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC;gBAC9C,OAAO,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;aAClC;YACD,qBAAqB;YACrB,QAAQ,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC;YAC7C,OAAO,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;SAClC;KACD;IAED,OAAO,KAAK,CAAC;AACd,CAAC,CAAC;AAEF;;;GAGG;AACH,MAAM,iBAAiB,GAAG,CAAC,IAAY,EAAE,EAAE;IAC1C,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;QAC7B,MAAM,IAAI,gBAAgB,CAAC,gIAAgI,CAAC,CAAC;KAC7J;AACF,CAAC,CAAC"}