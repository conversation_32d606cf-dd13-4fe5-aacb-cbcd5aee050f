{"version": 3, "file": "GraphResponseHandler.js", "sourceRoot": "", "sources": ["../../../src/GraphResponseHandler.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;;AAQH,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAE9C;;;;;;;GAOG;AACH,MAAM,CAAN,IAAY,YAKX;AALD,WAAY,YAAY;IACvB,uCAAuB,CAAA;IACvB,qCAAqB,CAAA;IACrB,mDAAmC,CAAA;IACnC,2DAA2C,CAAA;AAC5C,CAAC,EALW,YAAY,KAAZ,YAAY,QAKvB;AAED;;;;;GAKG;AAEH,IAAK,WAGJ;AAHD,WAAK,WAAW;IACf,wCAAyB,CAAA;IACzB,oDAAqC,CAAA;AACtC,CAAC,EAHI,WAAW,KAAX,WAAW,QAGf;AAED;;;;;GAKG;AACH,IAAK,mBAGJ;AAHD,WAAK,mBAAmB;IACvB,2FAAoE,CAAA;IACpE,4CAAqB,CAAA;AACtB,CAAC,EAHI,mBAAmB,KAAnB,mBAAmB,QAGvB;AAED;;;GAGG;AAEH,MAAM,OAAO,oBAAoB;IAChC;;;;;;;OAOG;IACK,MAAM,CAAC,qBAAqB,CAAC,WAAqB,EAAE,IAAkB;QAC7E,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE;YACrC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACtC,WAAW,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE;oBACrC,IAAI;wBACH,MAAM,MAAM,GAAG,IAAI,SAAS,EAAE,CAAC;wBAC/B,MAAM,MAAM,GAAG,MAAM,CAAC,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;wBACvD,OAAO,CAAC,MAAM,CAAC,CAAC;qBAChB;oBAAC,OAAO,KAAK,EAAE;wBACf,MAAM,CAAC,KAAK,CAAC,CAAC;qBACd;gBACF,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC;SACH;aAAM;YACN,OAAO,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SACzC;IACF,CAAC;IAED;;;;;;;;OAQG;IACK,MAAM,CAAO,eAAe,CAAC,WAAqB,EAAE,YAA2B;;YACtF,IAAI,WAAW,CAAC,MAAM,KAAK,GAAG,EAAE;gBAC/B,aAAa;gBACb,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;aACzB;YACD,IAAI,aAAkB,CAAC;YACvB,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YAC5D,QAAQ,YAAY,EAAE;gBACrB,KAAK,YAAY,CAAC,WAAW;oBAC5B,aAAa,GAAG,MAAM,WAAW,CAAC,WAAW,EAAE,CAAC;oBAChD,MAAM;gBACP,KAAK,YAAY,CAAC,IAAI;oBACrB,aAAa,GAAG,MAAM,WAAW,CAAC,IAAI,EAAE,CAAC;oBACzC,MAAM;gBACP,KAAK,YAAY,CAAC,QAAQ;oBACzB,aAAa,GAAG,MAAM,oBAAoB,CAAC,qBAAqB,CAAC,WAAW,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC;oBACrG,MAAM;gBACP,KAAK,YAAY,CAAC,IAAI;oBACrB,aAAa,GAAG,MAAM,WAAW,CAAC,IAAI,EAAE,CAAC;oBACzC,MAAM;gBACP,KAAK,YAAY,CAAC,MAAM;oBACvB,aAAa,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;oBACxD,MAAM;gBACP,KAAK,YAAY,CAAC,IAAI;oBACrB,aAAa,GAAG,MAAM,WAAW,CAAC,IAAI,EAAE,CAAC;oBACzC,MAAM;gBACP;oBACC,IAAI,WAAW,KAAK,IAAI,EAAE;wBACzB,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC3C,IAAI,IAAI,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;4BAC5D,aAAa,GAAG,MAAM,oBAAoB,CAAC,qBAAqB,CAAC,WAAW,EAAE,QAAwB,CAAC,CAAC;yBACxG;6BAAM,IAAI,IAAI,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;4BAChE,aAAa,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC;yBACnC;6BAAM,IAAI,QAAQ,KAAK,WAAW,CAAC,UAAU,EAAE;4BAC/C,aAAa,GAAG,MAAM,WAAW,CAAC,IAAI,EAAE,CAAC;yBACzC;6BAAM,IAAI,QAAQ,KAAK,WAAW,CAAC,gBAAgB,EAAE;4BACrD,aAAa,GAAG,MAAM,WAAW,CAAC,IAAI,EAAE,CAAC;yBACzC;6BAAM;4BACN,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;yBAClD;qBACD;yBAAM;wBACN;;;;;;;;;;2BAUG;wBACH,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;qBAClD;oBACD,MAAM;aACP;YACD,OAAO,aAAa,CAAC;QACtB,CAAC;KAAA;IAED;;;;;;;;;OASG;IACI,MAAM,CAAO,WAAW,CAAC,WAAqB,EAAE,YAA2B,EAAE,QAA+B;;YAClH,IAAI,YAAY,KAAK,YAAY,CAAC,GAAG,EAAE;gBACtC,OAAO,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;aACpC;iBAAM;gBACN,MAAM,QAAQ,GAAG,MAAM,oBAAoB,CAAC,eAAe,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;gBACvF,IAAI,WAAW,CAAC,EAAE,EAAE;oBACnB,kBAAkB;oBAClB,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;wBACnC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;qBACzB;yBAAM;wBACN,OAAO,QAAQ,CAAC;qBAChB;iBACD;qBAAM;oBACN,kBAAkB;oBAClB,MAAM,QAAQ,CAAC;iBACf;aACD;QACF,CAAC;KAAA;CACD"}