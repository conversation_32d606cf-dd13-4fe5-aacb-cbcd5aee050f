{"version": 3, "file": "TokenCredentialAuthenticationProvider.js", "sourceRoot": "", "sources": ["../../../../../src/authentication/azureTokenCredentials/TokenCredentialAuthenticationProvider.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;;AAIH,OAAO,EAAE,gBAAgB,EAAE,MAAM,wBAAwB,CAAC;AAI1D;;GAEG;AAEH;;;;;;GAMG;AACH,MAAM,OAAO,qCAAqC;IAajD;;;;;;;OAOG;IACH,YAAmB,eAAgC,EAAE,6BAA2E;QAC/H,IAAI,CAAC,eAAe,EAAE;YACrB,MAAM,IAAI,gBAAgB,CAAC,sGAAsG,CAAC,CAAC;SACnI;QACD,IAAI,CAAC,6BAA6B,EAAE;YACnC,MAAM,IAAI,gBAAgB,CAAC,yIAAyI,CAAC,CAAC;SACtK;QACD,IAAI,CAAC,6BAA6B,GAAG,6BAA6B,CAAC;QACnE,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;IACxC,CAAC;IAED;;;;;;OAMG;IACU,cAAc;;YAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC;YACzD,MAAM,KAAK,GAAG,IAAI,gBAAgB,EAAE,CAAC;YAErC,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;gBACnC,KAAK,CAAC,IAAI,GAAG,cAAc,CAAC;gBAC5B,KAAK,CAAC,OAAO,GAAG,+CAA+C,CAAC;gBAChE,MAAM,KAAK,CAAC;aACZ;YACD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC,CAAC;YACjH,IAAI,QAAQ,EAAE;gBACb,OAAO,QAAQ,CAAC,KAAK,CAAC;aACtB;YACD,KAAK,CAAC,OAAO,GAAG,8DAA8D,CAAC;YAC/E,KAAK,CAAC,IAAI,GAAG,2BAA2B,CAAC;YACzC,MAAM,KAAK,CAAC;QACb,CAAC;KAAA;CACD"}