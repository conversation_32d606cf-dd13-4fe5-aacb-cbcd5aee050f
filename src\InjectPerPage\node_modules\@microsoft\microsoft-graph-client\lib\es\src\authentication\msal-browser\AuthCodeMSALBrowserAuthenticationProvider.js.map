{"version": 3, "file": "AuthCodeMSALBrowserAuthenticationProvider.js", "sourceRoot": "", "sources": ["../../../../../src/authentication/msal-browser/AuthCodeMSALBrowserAuthenticationProvider.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;;AAEH;;GAEG;AAEH,OAAO,EAAwB,4BAA4B,EAAE,eAAe,EAA2B,MAAM,qBAAqB,CAAC;AAEnI,OAAO,EAAE,gBAAgB,EAAE,MAAM,wBAAwB,CAAC;AAI1D;;;;;GAKG;AACH,MAAM,OAAO,yCAAyC;IACrD;;;;;;;OAOG;IACH,YAA2B,uBAAgD,EAAU,OAAyD;QAAnH,4BAAuB,GAAvB,uBAAuB,CAAyB;QAAU,YAAO,GAAP,OAAO,CAAkD;QAC7I,IAAI,CAAC,OAAO,IAAI,CAAC,uBAAuB,EAAE;YACzC,MAAM,IAAI,gBAAgB,CAAC,mKAAmK,CAAC,CAAC;SAChM;IACF,CAAC;IAED;;;;;OAKG;IACU,cAAc;;YAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;YACnD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;YACrD,MAAM,KAAK,GAAG,IAAI,gBAAgB,EAAE,CAAC;YACrC,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;gBACnC,KAAK,CAAC,IAAI,GAAG,cAAc,CAAC;gBAC5B,KAAK,CAAC,OAAO,GAAG,+CAA+C,CAAC;gBAChE,MAAM,KAAK,CAAC;aACZ;YACD,IAAI;gBACH,MAAM,QAAQ,GAAyB,MAAM,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,CAAC;oBAC5F,MAAM;oBACN,OAAO;iBACP,CAAC,CAAC;gBACH,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;oBACvC,KAAK,CAAC,IAAI,GAAG,2BAA2B,CAAC;oBACzC,KAAK,CAAC,OAAO,GAAG,0DAA0D,CAAC;oBAC3E,MAAM,KAAK,CAAC;iBACZ;gBACD,OAAO,QAAQ,CAAC,WAAW,CAAC;aAC5B;YAAC,OAAO,KAAK,EAAE;gBACf,IAAI,KAAK,YAAY,4BAA4B,EAAE;oBAClD,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,KAAK,eAAe,CAAC,QAAQ,EAAE;wBAC9D,IAAI,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;qBAC9D;yBAAM,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,KAAK,eAAe,CAAC,KAAK,EAAE;wBAClE,MAAM,QAAQ,GAAyB,MAAM,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;wBACxG,OAAO,QAAQ,CAAC,WAAW,CAAC;qBAC5B;iBACD;qBAAM;oBACN,MAAM,KAAK,CAAC;iBACZ;aACD;QACF,CAAC;KAAA;CACD"}