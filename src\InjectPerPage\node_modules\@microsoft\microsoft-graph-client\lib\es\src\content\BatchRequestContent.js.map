{"version": 3, "file": "BatchRequestContent.js", "sourceRoot": "", "sources": ["../../../../src/content/BatchRequestContent.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;;AAEH;;GAEG;AACH,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAkEjD;;;GAGG;AACH,MAAM,OAAO,mBAAmB;IAqM/B;;;;;;OAMG;IACH,YAAmB,QAA6B;QAC/C,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,EAAE,CAAC;QAC1B,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;YACpC,MAAM,KAAK,GAAG,mBAAmB,CAAC,YAAY,CAAC;YAC/C,IAAI,QAAQ,CAAC,MAAM,GAAG,KAAK,EAAE;gBAC5B,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,uEAAuE,KAAK,EAAE,CAAC,CAAC;gBACxG,KAAK,CAAC,IAAI,GAAG,sBAAsB,CAAC;gBACpC,MAAM,KAAK,CAAC;aACZ;YACD,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE;gBAC3B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;aACrB;SACD;IACF,CAAC;IA3MD;;;;;;;;;;;;;;;OAeG;IAEK,MAAM,CAAC,oBAAoB,CAAC,QAAuC;QAC1E,MAAM,UAAU,GAAG,CAAC,IAAmC,EAAW,EAAE;YACnE,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAChC,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE;gBACjB,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC5B,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;oBAClE,OAAO,KAAK,CAAC;iBACb;gBACD,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;aACtB;YACD,OAAO,IAAI,CAAC;QACb,CAAC,CAAC;QACF,MAAM,QAAQ,GAAG,CAAC,IAAmC,EAAW,EAAE;YACjE,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAChC,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC1B,MAAM,YAAY,GAAqB,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACpD,IAAI,YAAY,CAAC,SAAS,KAAK,SAAS,IAAI,YAAY,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC9E,OAAO,KAAK,CAAC;aACb;YACD,IAAI,IAAI,GAAG,GAAG,CAAC;YACf,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE;gBACjB,MAAM,MAAM,GAAqB,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC9C,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;oBAChH,OAAO,KAAK,CAAC;iBACb;gBACD,IAAI,GAAG,GAAG,CAAC;gBACX,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;aACtB;YACD,OAAO,IAAI,CAAC;QACb,CAAC,CAAC;QACF,MAAM,MAAM,GAAG,CAAC,IAAmC,EAAW,EAAE;YAC/D,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAChC,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC1B,MAAM,YAAY,GAAqB,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACpD,IAAI,YAAoB,CAAC;YACzB,IAAI,YAAY,CAAC,SAAS,KAAK,SAAS,IAAI,YAAY,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;gBAChF,YAAY,GAAG,YAAY,CAAC,EAAE,CAAC;aAC/B;iBAAM;gBACN,IAAI,YAAY,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;oBACxC,MAAM,aAAa,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBAChD,IAAI,aAAa,KAAK,YAAY,CAAC,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE;wBACjE,YAAY,GAAG,aAAa,CAAC;qBAC7B;yBAAM;wBACN,OAAO,KAAK,CAAC;qBACb;iBACD;qBAAM;oBACN,OAAO,KAAK,CAAC;iBACb;aACD;YACD,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE;gBACjB,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC5B,IAAI,CAAC,MAAM,CAAC,SAAS,KAAK,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC,IAAI,YAAY,KAAK,MAAM,CAAC,EAAE,EAAE;oBACpG,OAAO,KAAK,CAAC;iBACb;gBACD,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;oBACpE,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,KAAK,YAAY,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,YAAY,CAAC,EAAE;wBAC1G,OAAO,KAAK,CAAC;qBACb;oBACD,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;wBAChC,OAAO,KAAK,CAAC;qBACb;iBACD;gBACD,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;aACtB;YACD,OAAO,IAAI,CAAC;QACb,CAAC,CAAC;QACF,IAAI,QAAQ,CAAC,IAAI,KAAK,CAAC,EAAE;YACxB,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;YACpF,KAAK,CAAC,IAAI,GAAG,sBAAsB,CAAC;YACpC,MAAM,KAAK,CAAC;SACZ;QACD,OAAO,UAAU,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC;IACvE,CAAC;IAED;;;;;;;OAOG;IACK,MAAM,CAAO,cAAc,CAAC,OAA0B;;YAC7D,MAAM,WAAW,GAAgB;gBAChC,GAAG,EAAE,EAAE;aACP,CAAC;YACF,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC;YAC9C,8CAA8C;YAC9C,WAAW,CAAC,GAAG,GAAG,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC;YAC5G,WAAW,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;YACpC,MAAM,OAAO,GAAG,EAAE,CAAC;YACnB,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;gBACtC,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YACtB,CAAC,CAAC,CAAC;YACH,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE;gBAChC,WAAW,CAAC,OAAO,GAAG,OAAO,CAAC;aAC9B;YACD,IAAI,OAAO,CAAC,MAAM,KAAK,aAAa,CAAC,KAAK,IAAI,OAAO,CAAC,MAAM,KAAK,aAAa,CAAC,IAAI,IAAI,OAAO,CAAC,MAAM,KAAK,aAAa,CAAC,GAAG,EAAE;gBAC5H,WAAW,CAAC,IAAI,GAAG,MAAM,mBAAmB,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;aACrE;YACD;;eAEG;YACH,OAAO,WAAW,CAAC;QACpB,CAAC;KAAA;IAED;;;;;;;OAOG;IACK,MAAM,CAAO,cAAc,CAAC,OAA0B;;YAC7D,IAAI,UAAU,GAAG,KAAK,CAAC;YACvB,IAAI,IAAI,CAAC;YACT,IAAI;gBACH,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;gBACjC,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAC7B,UAAU,GAAG,IAAI,CAAC;aAClB;YAAC,OAAO,CAAC,EAAE;gBACX,4BAA4B;aAC5B;YACD,IAAI,CAAC,UAAU,EAAE;gBAChB,IAAI;oBACH,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;wBAChC,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;wBAClC,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;wBAChC,IAAI,GAAG,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;4BACpC,MAAM,CAAC,gBAAgB,CACtB,MAAM,EACN,GAAG,EAAE;gCACJ,MAAM,OAAO,GAAG,MAAM,CAAC,MAAgB,CAAC;gCACxC;;;;;;;;mCAQG;gCACH,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,iDAAiD,CAAC,CAAC;gCAC5E,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gCACrC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;4BACtB,CAAC,EACD,KAAK,CACL,CAAC;4BACF,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;wBAC5B,CAAC,CAAC,CAAC;qBACH;yBAAM,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;wBACzC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,MAAM,EAAE,CAAC;wBACtC,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;qBACjC;oBACD,UAAU,GAAG,IAAI,CAAC;iBAClB;gBAAC,OAAO,CAAC,EAAE;oBACX,4BAA4B;iBAC5B;aACD;YACD,OAAO,IAAI,CAAC;QACb,CAAC;KAAA;IAwBD;;;;;OAKG;IACI,UAAU,CAAC,OAAyB;QAC1C,MAAM,KAAK,GAAG,mBAAmB,CAAC,YAAY,CAAC;QAC/C,IAAI,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE;YACtB,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;YAClF,KAAK,CAAC,IAAI,GAAG,sBAAsB,CAAC;YACpC,MAAM,KAAK,CAAC;SACZ;QACD,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,KAAK,EAAE;YACjC,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,uEAAuE,KAAK,EAAE,CAAC,CAAC;YACxG,KAAK,CAAC,IAAI,GAAG,sBAAsB,CAAC;YACpC,MAAM,KAAK,CAAC;SACZ;QACD,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;YAClC,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,oCAAoC,OAAO,CAAC,EAAE,sCAAsC,CAAC,CAAC;YAC9G,KAAK,CAAC,IAAI,GAAG,2BAA2B,CAAC;YACzC,MAAM,KAAK,CAAC;SACZ;QACD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QACvC,OAAO,OAAO,CAAC,EAAE,CAAC;IACnB,CAAC;IAED;;;;;OAKG;IACI,aAAa,CAAC,SAAiB;QACrC,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;QACzC,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC1B;;WAEG;QACH,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE;YACjB,MAAM,YAAY,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAC5C,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;gBACxC,MAAM,KAAK,GAAG,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;gBAC9C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;oBACjB,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;iBAC9B;gBACD,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;oBAC9B,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;iBAC9B;aACD;YACD,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;SACtB;QACD,OAAO,YAAY,CAAC;IACrB,CAAC;IAED;;;;;OAKG;IACU,UAAU;;YACtB,MAAM,QAAQ,GAAuB,EAAE,CAAC;YACxC,MAAM,WAAW,GAAqB;gBACrC,QAAQ;aACR,CAAC;YACF,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;YACzC,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC1B,IAAI,GAAG,CAAC,IAAI,EAAE;gBACb,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;gBACnF,KAAK,CAAC,IAAI,GAAG,eAAe,CAAC;gBAC7B,MAAM,KAAK,CAAC;aACZ;YACD,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;gBAC7D,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC;;;gHAGqF,CAAC,CAAC;gBAC/G,KAAK,CAAC,IAAI,GAAG,oBAAoB,CAAC;gBAClC,MAAM,KAAK,CAAC;aACZ;YACD,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE;gBACjB,MAAM,WAAW,GAAqB,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACnD,MAAM,gBAAgB,GAAqB,CAAC,MAAM,mBAAmB,CAAC,cAAc,CAAC,WAAW,CAAC,OAA4B,CAAC,CAAqB,CAAC;gBACpJ;;;;mBAIG;gBACH,IAAI,gBAAgB,CAAC,IAAI,KAAK,SAAS,IAAI,CAAC,gBAAgB,CAAC,OAAO,KAAK,SAAS,IAAI,gBAAgB,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,SAAS,CAAC,EAAE;oBAC9I,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,qDAAqD,WAAW,CAAC,EAAE,oEAAoE,CAAC,CAAC;oBACjK,KAAK,CAAC,IAAI,GAAG,6BAA6B,CAAC;oBAC3C,MAAM,KAAK,CAAC;iBACZ;gBACD,gBAAgB,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,CAAC;gBACrC,IAAI,WAAW,CAAC,SAAS,KAAK,SAAS,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC5E,gBAAgB,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;iBACnD;gBACD,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAChC,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;aACtB;YACD,WAAW,CAAC,QAAQ,GAAG,QAAQ,CAAC;YAChC,OAAO,WAAW,CAAC;QACpB,CAAC;KAAA;IAED;;;;;;OAMG;IACI,aAAa,CAAC,WAAmB,EAAE,YAAqB;QAC9D,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;YACpC,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,aAAa,WAAW,uCAAuC,CAAC,CAAC;YACzF,KAAK,CAAC,IAAI,GAAG,mBAAmB,CAAC;YACjC,MAAM,KAAK,CAAC;SACZ;QACD,IAAI,OAAO,YAAY,KAAK,WAAW,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;YAC5E,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,cAAc,YAAY,uCAAuC,CAAC,CAAC;YAC3F,KAAK,CAAC,IAAI,GAAG,oBAAoB,CAAC;YAClC,MAAM,KAAK,CAAC;SACZ;QACD,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;YACxC,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YACjD,IAAI,SAAS,CAAC,SAAS,KAAK,SAAS,EAAE;gBACtC,SAAS,CAAC,SAAS,GAAG,EAAE,CAAC;aACzB;YACD,IAAI,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE;gBACrD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,cAAc,YAAY,qCAAqC,WAAW,EAAE,CAAC,CAAC;gBACtG,KAAK,CAAC,IAAI,GAAG,sBAAsB,CAAC;gBACpC,MAAM,KAAK,CAAC;aACZ;YACD,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SACvC;aAAM;YACN,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;YACzC,IAAI,IAAI,CAAC;YACT,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,WAAW,EAAE;gBACpD,IAAI,GAAG,GAAG,CAAC;gBACX,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;aACtB;YACD,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;gBAChC,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC1B,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,KAAK,SAAS,EAAE;oBACzC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,EAAE,CAAC;iBAC5B;gBACD,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;oBAC/C,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,cAAc,GAAG,qCAAqC,WAAW,EAAE,CAAC,CAAC;oBAC7F,KAAK,CAAC,IAAI,GAAG,sBAAsB,CAAC;oBACpC,MAAM,KAAK,CAAC;iBACZ;gBACD,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACjC;iBAAM;gBACN,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,wBAAwB,YAAY,kDAAkD,CAAC,CAAC;gBAChH,KAAK,CAAC,IAAI,GAAG,6BAA6B,CAAC;gBAC3C,MAAM,KAAK,CAAC;aACZ;SACD;IACF,CAAC;IAED;;;;;;OAMG;IACI,gBAAgB,CAAC,WAAmB,EAAE,YAAqB;QACjE,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAC/C,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACxG,OAAO,KAAK,CAAC;SACb;QACD,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;YACxC,MAAM,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACtD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;gBACjB,OAAO,KAAK,CAAC;aACb;YACD,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACnC,OAAO,IAAI,CAAC;SACZ;aAAM;YACN,OAAO,OAAO,CAAC,SAAS,CAAC;YACzB,OAAO,IAAI,CAAC;SACZ;IACF,CAAC;;AAnZD;;;;GAIG;AACY,gCAAY,GAAG,EAAE,CAAC"}