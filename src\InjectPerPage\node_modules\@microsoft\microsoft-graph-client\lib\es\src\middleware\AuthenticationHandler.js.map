{"version": 3, "file": "AuthenticationHandler.js", "sourceRoot": "", "sources": ["../../../../src/middleware/AuthenticationHandler.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;;AAEH;;GAEG;AAEH,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAK/D,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,EAAE,mBAAmB,EAAE,MAAM,kBAAkB,CAAC;AACvD,OAAO,EAAE,4BAA4B,EAAE,MAAM,wCAAwC,CAAC;AACtF,OAAO,EAAE,gBAAgB,EAAE,uBAAuB,EAAE,MAAM,mCAAmC,CAAC;AAE9F;;;;GAIG;AACH,MAAM,OAAO,qBAAqB;IAmBjC;;;;;OAKG;IACH,YAAmB,sBAA8C;QAChE,IAAI,CAAC,sBAAsB,GAAG,sBAAsB,CAAC;IACtD,CAAC;IAED;;;;;;OAMG;IACU,OAAO,CAAC,OAAgB;;YACpC,MAAM,GAAG,GAAG,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC;YACxF,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,YAAY,CAAC,GAAG,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE;gBACvF,IAAI,OAAqC,CAAC;gBAC1C,IAAI,OAAO,CAAC,iBAAiB,YAAY,iBAAiB,EAAE;oBAC3D,OAAO,GAAG,OAAO,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,4BAA4B,CAAiC,CAAC;iBACvH;gBACD,IAAI,sBAA8C,CAAC;gBACnD,IAAI,6BAA4D,CAAC;gBACjE,IAAI,OAAO,EAAE;oBACZ,sBAAsB,GAAG,OAAO,CAAC,sBAAsB,CAAC;oBACxD,6BAA6B,GAAG,OAAO,CAAC,6BAA6B,CAAC;iBACtE;gBACD,IAAI,CAAC,sBAAsB,EAAE;oBAC5B,sBAAsB,GAAG,IAAI,CAAC,sBAAsB,CAAC;iBACrD;gBACD,MAAM,KAAK,GAAW,MAAM,sBAAsB,CAAC,cAAc,CAAC,6BAA6B,CAAC,CAAC;gBACjG,MAAM,SAAS,GAAG,UAAU,KAAK,EAAE,CAAC;gBACpC,mBAAmB,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,qBAAqB,CAAC,oBAAoB,EAAE,SAAS,CAAC,CAAC;gBAC7G,uBAAuB,CAAC,sBAAsB,CAAC,OAAO,EAAE,gBAAgB,CAAC,8BAA8B,CAAC,CAAC;aACzG;iBAAM;gBACN,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE;oBAC5B,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,CAAC;iBAC3E;aACD;YACD,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACnD,CAAC;KAAA;IAED;;;;;OAKG;IACI,OAAO,CAAC,IAAgB;QAC9B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC5B,CAAC;;AAvED;;;GAGG;AACY,0CAAoB,GAAG,eAAe,CAAC"}