/**
 * -------------------------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation.  All Rights Reserved.  Licensed under the MIT License.
 * See License in the project root for license information.
 * -------------------------------------------------------------------------------------------
 */
/**
 * @module MiddlewareUtil
 */
import { FetchOptions } from "../IFetchOptions";
/**
 * @constant
 * To generate the UUID
 * @returns The UUID string
 */
export declare const generateUUID: () => string;
/**
 * @constant
 * To get the request header from the request
 * @param {RequestInfo} request - The request object or the url string
 * @param {FetchOptions|undefined} options - The request options object
 * @param {string} key - The header key string
 * @returns A header value for the given key from the request
 */
export declare const getRequestHeader: (request: RequestInfo, options: FetchOptions | undefined, key: string) => string | null;
/**
 * @constant
 * To set the header value to the given request
 * @param {RequestInfo} request - The request object or the url string
 * @param {FetchOptions|undefined} options - The request options object
 * @param {string} key - The header key string
 * @param {string } value - The header value string
 * @returns Nothing
 */
export declare const setRequestHeader: (request: RequestInfo, options: FetchOptions | undefined, key: string, value: string) => void;
/**
 * @constant
 * To append the header value to the given request
 * @param {RequestInfo} request - The request object or the url string
 * @param {FetchOptions|undefined} options - The request options object
 * @param {string} key - The header key string
 * @param {string } value - The header value string
 * @returns Nothing
 */
export declare const appendRequestHeader: (request: RequestInfo, options: FetchOptions | undefined, key: string, value: string) => void;
/**
 * @constant
 * To clone the request with the new url
 * @param {string} url - The new url string
 * @param {Request} request - The request object
 * @returns A promise that resolves to request object
 */
export declare const cloneRequestWithNewUrl: (newUrl: string, request: Request) => Promise<Request>;
