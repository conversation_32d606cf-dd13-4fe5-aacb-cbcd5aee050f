{"version": 3, "file": "MiddlewareUtil.js", "sourceRoot": "", "sources": ["../../../../src/middleware/MiddlewareUtil.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;;AAQH;;;;GAIG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG,GAAW,EAAE;IACxC,IAAI,IAAI,GAAG,EAAE,CAAC;IACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;QAC5B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE;YAChD,IAAI,IAAI,GAAG,CAAC;SACZ;QACD,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;KACpD;IACD,OAAO,IAAI,CAAC;AACb,CAAC,CAAC;AAEF;;;;;;;GAOG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,OAAoB,EAAE,OAAiC,EAAE,GAAW,EAAiB,EAAE;IACvH,IAAI,KAAK,GAAW,IAAI,CAAC;IACzB,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,YAAY,OAAO,EAAE;QACjE,KAAK,GAAI,OAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;KAC9C;SAAM,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE;QAC3E,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,OAAO,YAAY,OAAO,EAAE;YACzE,KAAK,GAAI,OAAO,CAAC,OAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SAC9C;aAAM,IAAI,OAAO,CAAC,OAAO,YAAY,KAAK,EAAE;YAC5C,MAAM,OAAO,GAAG,OAAO,CAAC,OAAqB,CAAC;YAC9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC/C,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;oBAC1B,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,MAAM;iBACN;aACD;SACD;aAAM,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;YAC9C,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;SAC7B;KACD;IACD,OAAO,KAAK,CAAC;AACd,CAAC,CAAC;AAEF;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,OAAoB,EAAE,OAAiC,EAAE,GAAW,EAAE,KAAa,EAAQ,EAAE;IAC7H,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,YAAY,OAAO,EAAE;QAChE,OAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;KAC7C;SAAM,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;QAC1C,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE;YAClC,OAAO,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC;gBAC7B,CAAC,GAAG,CAAC,EAAE,KAAK;aACZ,CAAC,CAAC;SACH;aAAM;YACN,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,OAAO,YAAY,OAAO,EAAE;gBACxE,OAAO,CAAC,OAAmB,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;aAC7C;iBAAM,IAAI,OAAO,CAAC,OAAO,YAAY,KAAK,EAAE;gBAC5C,IAAI,CAAC,GAAG,CAAC,CAAC;gBACV,MAAM,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;gBACjC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;oBAClB,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;oBAClC,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;wBACtB,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;wBAClB,MAAM;qBACN;iBACD;gBACD,IAAI,CAAC,KAAK,CAAC,EAAE;oBACX,OAAO,CAAC,OAAsB,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;iBACnD;aACD;iBAAM;gBACN,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;aACjD;SACD;KACD;AACF,CAAC,CAAC;AAEF;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAG,CAAC,OAAoB,EAAE,OAAiC,EAAE,GAAW,EAAE,KAAa,EAAQ,EAAE;IAChI,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,YAAY,OAAO,EAAE;QAChE,OAAmB,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;KAChD;SAAM,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;QAC1C,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE;YAClC,OAAO,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC;gBAC7B,CAAC,GAAG,CAAC,EAAE,KAAK;aACZ,CAAC,CAAC;SACH;aAAM;YACN,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,OAAO,YAAY,OAAO,EAAE;gBACxE,OAAO,CAAC,OAAmB,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;aAChD;iBAAM,IAAI,OAAO,CAAC,OAAO,YAAY,KAAK,EAAE;gBAC3C,OAAO,CAAC,OAAsB,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;aACnD;iBAAM,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE;gBACzC,OAAO,CAAC,OAAO,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC;aACnC;iBAAM,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;gBAC9C,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;aAC7B;iBAAM;gBACN,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;aACrC;SACD;KACD;AACF,CAAC,CAAC;AAEF;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,sBAAsB,GAAG,CAAO,MAAc,EAAE,OAAgB,EAAoB,EAAE;IAClG,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,MAAM,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IAC3G,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,cAAc,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;IAChI,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,cAAc,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAC;AACnJ,CAAC,CAAA,CAAC"}