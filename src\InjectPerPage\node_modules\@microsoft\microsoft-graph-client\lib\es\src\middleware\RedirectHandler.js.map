{"version": 3, "file": "RedirectHandler.js", "sourceRoot": "", "sources": ["../../../../src/middleware/RedirectHandler.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;;AAOH,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAEjD,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,EAAE,sBAAsB,EAAE,MAAM,kBAAkB,CAAC;AAC1D,OAAO,EAAE,sBAAsB,EAAE,MAAM,kCAAkC,CAAC;AAC1E,OAAO,EAAE,gBAAgB,EAAE,uBAAuB,EAAE,MAAM,mCAAmC,CAAC;AAE9F;;;;;GAKG;AACH,MAAM,OAAO,eAAe;IAsD3B;;;;;;OAMG;IAEH,YAAmB,UAAkC,IAAI,sBAAsB,EAAE;QAChF,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACxB,CAAC;IAED;;;;;OAKG;IACK,UAAU,CAAC,QAAkB;QACpC,OAAO,eAAe,CAAC,qBAAqB,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IAC9E,CAAC;IAED;;;;;OAKG;IACK,iBAAiB,CAAC,QAAkB;QAC3C,OAAO,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;IAC9D,CAAC;IAED;;;;;OAKG;IACK,iBAAiB,CAAC,QAAkB;QAC3C,OAAO,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;IAC9D,CAAC;IAED;;;;;OAKG;IACK,aAAa,CAAC,GAAW;QAChC,OAAO,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;IAClC,CAAC;IAED;;;;;;OAMG;IACK,6BAA6B,CAAC,UAAkB,EAAE,WAAmB;QAC5E,MAAM,eAAe,GAAG,8BAA8B,CAAC;QACvD,MAAM,cAAc,GAAa,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAClE,IAAI,gBAAwB,CAAC;QAC7B,IAAI,iBAAyB,CAAC;QAC9B,IAAI,cAAc,KAAK,IAAI,EAAE;YAC5B,gBAAgB,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;SACrC;QACD,MAAM,eAAe,GAAa,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACpE,IAAI,eAAe,KAAK,IAAI,EAAE;YAC7B,iBAAiB,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;SACvC;QACD,OAAO,OAAO,gBAAgB,KAAK,WAAW,IAAI,OAAO,iBAAiB,KAAK,WAAW,IAAI,gBAAgB,KAAK,iBAAiB,CAAC;IACtI,CAAC;IAED;;;;;;;OAOG;IACW,gBAAgB,CAAC,WAAmB,EAAE,OAAgB;;YACnE,OAAO,CAAC,OAAO,GAAG,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,sBAAsB,CAAC,WAAW,EAAE,OAAO,CAAC,OAAkB,CAAC,CAAC;QAC7I,CAAC;KAAA;IAED;;;;;OAKG;IACK,UAAU,CAAC,OAAgB;QAClC,IAAI,OAA+B,CAAC;QACpC,IAAI,OAAO,CAAC,iBAAiB,YAAY,iBAAiB,EAAE;YAC3D,OAAO,GAAG,OAAO,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,sBAAsB,CAA2B,CAAC;SAC3G;QACD,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;YACnC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,sBAAsB,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SACpE;QACD,OAAO,OAAO,CAAC;IAChB,CAAC;IAED;;;;;;;;OAQG;IACW,mBAAmB,CAAC,OAAgB,EAAE,aAAqB,EAAE,OAA+B;;YACzG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAC3C,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;YAClC,IAAI,aAAa,GAAG,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;gBAC9I,EAAE,aAAa,CAAC;gBAChB,IAAI,QAAQ,CAAC,MAAM,KAAK,eAAe,CAAC,qBAAqB,EAAE;oBAC9D,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,aAAa,CAAC,GAAG,CAAC;oBAC3C,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;iBAC5B;qBAAM;oBACN,MAAM,WAAW,GAAW,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;oBAC7D,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,6BAA6B,CAAC,QAAQ,CAAC,GAAG,EAAE,WAAW,CAAC,EAAE;wBACtG,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC;qBACrE;oBACD,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;iBAClD;gBACD,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;aAChE;iBAAM;gBACN,OAAO;aACP;QACF,CAAC;KAAA;IAED;;;;;;OAMG;IACU,OAAO,CAAC,OAAgB;;YACpC,MAAM,aAAa,GAAG,CAAC,CAAC;YACxB,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACzC,OAAO,CAAC,OAAO,CAAC,QAAQ,GAAG,eAAe,CAAC,eAAe,CAAC;YAC3D,uBAAuB,CAAC,sBAAsB,CAAC,OAAO,EAAE,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;YACnG,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;QACxE,CAAC;KAAA;IAED;;;;;OAKG;IACI,OAAO,CAAC,IAAgB;QAC9B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC5B,CAAC;;AAjND;;;;GAIG;AACY,qCAAqB,GAAa;IAChD,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG,EAAE,oBAAoB;CACzB,CAAC;AAEF;;;;GAIG;AACY,qCAAqB,GAAG,GAAG,CAAC;AAE3C;;;;GAIG;AACY,+BAAe,GAAG,UAAU,CAAC;AAE5C;;;;GAIG;AACY,oCAAoB,GAAG,eAAe,CAAC;AAEtD;;;;GAIG;AACY,+BAAe,GAAoB,QAAQ,CAAC"}