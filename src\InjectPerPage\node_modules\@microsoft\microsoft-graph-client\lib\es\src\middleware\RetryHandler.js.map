{"version": 3, "file": "RetryHandler.js", "sourceRoot": "", "sources": ["../../../../src/middleware/RetryHandler.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;;AAQH,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAEjD,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,MAAM,kBAAkB,CAAC;AACtE,OAAO,EAAE,mBAAmB,EAAE,MAAM,+BAA+B,CAAC;AACpE,OAAO,EAAE,gBAAgB,EAAE,uBAAuB,EAAE,MAAM,mCAAmC,CAAC;AAE9F;;;;GAIG;AACH,MAAM,OAAO,YAAY;IAsCxB;;;;;;OAMG;IACH,YAAmB,UAA+B,IAAI,mBAAmB,EAAE;QAC1E,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACxB,CAAC;IAED;;;;;;OAMG;IACK,OAAO,CAAC,QAAkB;QACjC,OAAO,YAAY,CAAC,kBAAkB,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IACxE,CAAC;IAED;;;;;;OAMG;IACK,UAAU,CAAC,OAAoB,EAAE,OAAiC;QACzE,MAAM,MAAM,GAAG,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAE,OAAmB,CAAC,MAAM,CAAC;QAC1F,MAAM,gBAAgB,GAAY,MAAM,KAAK,aAAa,CAAC,GAAG,IAAI,MAAM,KAAK,aAAa,CAAC,KAAK,IAAI,MAAM,KAAK,aAAa,CAAC,IAAI,CAAC;QAClI,IAAI,gBAAgB,EAAE;YACrB,MAAM,QAAQ,GAAG,gBAAgB,CAAC,OAAO,EAAE,OAAO,EAAE,cAAc,CAAC,KAAK,0BAA0B,CAAC;YACnG,IAAI,QAAQ,EAAE;gBACb,OAAO,KAAK,CAAC;aACb;SACD;QACD,OAAO,IAAI,CAAC;IACb,CAAC;IAED;;;;;;;OAOG;IACK,QAAQ,CAAC,QAAkB,EAAE,aAAqB,EAAE,KAAa;QACxE,MAAM,aAAa,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7D,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACjH,IAAI,QAAgB,CAAC;QACrB,IAAI,UAAU,KAAK,IAAI,EAAE;YACxB,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE;gBACrC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;aAC5E;iBAAM;gBACN,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;aAC9B;SACD;aAAM;YACN,gDAAgD;YAChD,QAAQ,GAAG,aAAa,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,aAAa,CAAC,GAAG,KAAK,GAAG,aAAa,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,aAAa,EAAE,CAAC;SAClI;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,aAAa,EAAE,CAAC,CAAC;IACzE,CAAC;IAED;;;;;OAKG;IACK,yBAAyB,CAAC,QAAgB;QACjD,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,SAAA,CAAC,EAAI,QAAQ,CAAA,GAAG,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC;IAED;;;;;;OAMG;IACW,KAAK,CAAC,YAAoB;;YACvC,MAAM,iBAAiB,GAAG,YAAY,GAAG,IAAI,CAAC;YAC9C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC,CAAC;QACzE,CAAC;KAAA;IAEO,UAAU,CAAC,OAAgB;QAClC,IAAI,OAA4B,CAAC;QACjC,IAAI,OAAO,CAAC,iBAAiB,YAAY,iBAAiB,EAAE;YAC3D,OAAO,GAAG,OAAO,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAwB,CAAC;SAC1G;QACD,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;YACnC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,mBAAmB,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SACjE;QACD,OAAO,OAAO,CAAC;IAChB,CAAC;IAED;;;;;;;;OAQG;IACW,gBAAgB,CAAC,OAAgB,EAAE,aAAqB,EAAE,OAA4B;;YACnG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAC3C,IAAI,aAAa,GAAG,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,aAAa,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,EAAE;gBACvO,EAAE,aAAa,CAAC;gBAChB,gBAAgB,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,YAAY,CAAC,oBAAoB,EAAE,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAChH,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,aAAa,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;gBAC5E,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACxB,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;aACpE;iBAAM;gBACN,OAAO;aACP;QACF,CAAC;KAAA;IAED;;;;;;OAMG;IACU,OAAO,CAAC,OAAgB;;YACpC,MAAM,aAAa,GAAG,CAAC,CAAC;YACxB,MAAM,OAAO,GAAwB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YAC9D,uBAAuB,CAAC,sBAAsB,CAAC,OAAO,EAAE,gBAAgB,CAAC,qBAAqB,CAAC,CAAC;YAChG,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;QACrE,CAAC;KAAA;IAED;;;;;OAKG;IACI,OAAO,CAAC,IAAgB;QAC9B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC5B,CAAC;;AApLD;;;;GAIG;AACY,+BAAkB,GAAa;IAC7C,GAAG;IACH,GAAG;IACH,GAAG,EAAE,kBAAkB;CACvB,CAAC;AAEF;;;;GAIG;AACY,iCAAoB,GAAG,eAAe,CAAC;AAEtD;;;;GAIG;AACY,+BAAkB,GAAG,aAAa,CAAC"}