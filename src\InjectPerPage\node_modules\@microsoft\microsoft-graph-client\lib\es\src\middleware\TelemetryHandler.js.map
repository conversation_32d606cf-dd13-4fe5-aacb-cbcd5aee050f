{"version": 3, "file": "TelemetryHandler.js", "sourceRoot": "", "sources": ["../../../../src/middleware/TelemetryHandler.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;;AAEH;;GAEG;AACH,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAE/D,OAAO,EAAE,eAAe,EAAE,MAAM,YAAY,CAAC;AAE7C,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,EAAE,mBAAmB,EAAE,YAAY,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,MAAM,kBAAkB,CAAC;AACzG,OAAO,EAAE,uBAAuB,EAAE,MAAM,mCAAmC,CAAC;AAE5E;;;;GAIG;AACH,MAAM,OAAO,gBAAgB;IAmC5B;;;;;;OAMG;IACU,OAAO,CAAC,OAAgB;;YACpC,MAAM,GAAG,GAAG,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC;YACxF,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,YAAY,CAAC,GAAG,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE;gBACvF,wDAAwD;gBACxD,kGAAkG;gBAClG,IAAI,eAAe,GAAW,gBAAgB,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;gBAC5H,IAAI,CAAC,eAAe,EAAE;oBACrB,eAAe,GAAG,YAAY,EAAE,CAAC;oBACjC,gBAAgB,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,gBAAgB,CAAC,wBAAwB,EAAE,eAAe,CAAC,CAAC;iBAC/G;gBACD,IAAI,eAAe,GAAG,GAAG,gBAAgB,CAAC,YAAY,IAAI,eAAe,EAAE,CAAC;gBAC5E,IAAI,OAAgC,CAAC;gBACrC,IAAI,OAAO,CAAC,iBAAiB,YAAY,iBAAiB,EAAE;oBAC3D,OAAO,GAAG,OAAO,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,uBAAuB,CAA4B,CAAC;iBAC7G;gBACD,IAAI,OAAO,EAAE;oBACZ,MAAM,YAAY,GAAW,OAAO,CAAC,eAAe,EAAE,CAAC;oBACvD,eAAe,IAAI,KAAK,gBAAgB,CAAC,oBAAoB,IAAI,YAAY,GAAG,CAAC;iBACjF;gBACD,mBAAmB,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,gBAAgB,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC;aAC5G;iBAAM;gBACN,0DAA0D;gBAC1D,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;gBAC1E,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;aACpE;YACD,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACnD,CAAC;KAAA;IAED;;;;;OAKG;IACI,OAAO,CAAC,IAAgB;QAC9B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC5B,CAAC;;AA7ED;;;;GAIG;AACY,yCAAwB,GAAG,mBAAmB,CAAC;AAE9D;;;;GAIG;AACY,mCAAkB,GAAG,YAAY,CAAC;AAEjD;;;;GAIG;AACY,6BAAY,GAAG,UAAU,CAAC;AAEzC;;;;GAIG;AACY,qCAAoB,GAAG,cAAc,CAAC"}