{"version": 3, "file": "RetryHandlerOptions.js", "sourceRoot": "", "sources": ["../../../../../src/middleware/options/RetryHandlerOptions.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAeH;;;;GAIG;AAEH,MAAM,OAAO,mBAAmB;IAqD/B;;;;;;;;OAQG;IACH,YAAmB,QAAgB,mBAAmB,CAAC,aAAa,EAAE,aAAqB,mBAAmB,CAAC,mBAAmB,EAAE,cAA2B,mBAAmB,CAAC,kBAAkB;QACpM,IAAI,KAAK,GAAG,mBAAmB,CAAC,SAAS,IAAI,UAAU,GAAG,mBAAmB,CAAC,eAAe,EAAE;YAC9F,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,gDAAgD,mBAAmB,CAAC,SAAS,QAAQ,mBAAmB,CAAC,eAAe,EAAE,CAAC,CAAC;YACpJ,KAAK,CAAC,IAAI,GAAG,kBAAkB,CAAC;YAChC,MAAM,KAAK,CAAC;SACZ;aAAM,IAAI,KAAK,GAAG,mBAAmB,CAAC,SAAS,EAAE;YACjD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,iCAAiC,mBAAmB,CAAC,SAAS,EAAE,CAAC,CAAC;YAC1F,KAAK,CAAC,IAAI,GAAG,kBAAkB,CAAC;YAChC,MAAM,KAAK,CAAC;SACZ;aAAM,IAAI,UAAU,GAAG,mBAAmB,CAAC,eAAe,EAAE;YAC5D,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,sCAAsC,mBAAmB,CAAC,eAAe,EAAE,CAAC,CAAC;YACrG,KAAK,CAAC,IAAI,GAAG,kBAAkB,CAAC;YAChC,MAAM,KAAK,CAAC;SACZ;aAAM,IAAI,KAAK,GAAG,CAAC,IAAI,UAAU,GAAG,CAAC,EAAE;YACvC,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;YACvE,KAAK,CAAC,IAAI,GAAG,sBAAsB,CAAC;YACpC,MAAM,KAAK,CAAC;SACZ;aAAM,IAAI,KAAK,GAAG,CAAC,EAAE;YACrB,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;YACxD,KAAK,CAAC,IAAI,GAAG,sBAAsB,CAAC;YACpC,MAAM,KAAK,CAAC;SACZ;aAAM,IAAI,UAAU,GAAG,CAAC,EAAE;YAC1B,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;YAC7D,KAAK,CAAC,IAAI,GAAG,sBAAsB,CAAC;YACpC,MAAM,KAAK,CAAC;SACZ;QACD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,mBAAmB,CAAC,SAAS,CAAC,CAAC;QAC5D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,mBAAmB,CAAC,eAAe,CAAC,CAAC;QAC5E,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IAChC,CAAC;IAED;;;;OAIG;IACI,WAAW;QACjB,OAAO,mBAAmB,CAAC,SAAS,CAAC;IACtC,CAAC;;AAnGD;;;;GAIG;AACY,iCAAa,GAAG,CAAC,CAAC;AAEjC;;;;GAIG;AACY,uCAAmB,GAAG,CAAC,CAAC;AAEvC;;;;GAIG;AACY,6BAAS,GAAG,GAAG,CAAC;AAE/B;;;;GAIG;AACY,mCAAe,GAAG,EAAE,CAAC;AAoBpC;;;GAGG;AACY,sCAAkB,GAAgB,GAAG,EAAE,CAAC,IAAI,CAAC"}