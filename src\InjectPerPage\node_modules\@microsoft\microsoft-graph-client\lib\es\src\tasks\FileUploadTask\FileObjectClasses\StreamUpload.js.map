{"version": 3, "file": "StreamUpload.js", "sourceRoot": "", "sources": ["../../../../../../src/tasks/FileUploadTask/FileObjectClasses/StreamUpload.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,gBAAgB,EAAE,MAAM,2BAA2B,CAAC;AAe7D;;;GAGG;AACH,MAAM,OAAO,YAAY;IAQxB,YAA0B,OAAmB,EAAS,IAAY,EAAS,IAAY;QAA7D,YAAO,GAAP,OAAO,CAAY;QAAS,SAAI,GAAJ,IAAI,CAAQ;QAAS,SAAI,GAAJ,IAAI,CAAQ;QACtF,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE;YAC/B,MAAM,IAAI,gBAAgB,CAAC,mFAAmF,CAAC,CAAC;SAChH;IACF,CAAC;IAED;;;;;OAKG;IACU,SAAS,CAAC,KAAY;;YAClC,IAAI,SAAS,GAAG,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC;YACpD;;eAEG;YACH,MAAM,IAAI,GAAG,EAAE,CAAC;YAEhB;;;;;;eAMG;YACH,IAAI,IAAI,CAAC,aAAa,EAAE;gBACvB,IAAI,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,EAAE;oBACvD,MAAM,IAAI,gBAAgB,CAAC,iHAAiH,CAAC,CAAC;iBAC9I;gBAED,IAAI,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,EAAE;oBACvD,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,CAAC;oBAC3D,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,CAAC;oBAE3D,kEAAkE;oBAClE,IAAI,KAAK,CAAC,QAAQ,KAAK,gBAAgB,IAAI,KAAK,CAAC,QAAQ,KAAK,gBAAgB,EAAE;wBAC/E,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;qBACpC;oBAED;;;;;uBAKG;oBACH,IAAI,KAAK,CAAC,QAAQ,KAAK,gBAAgB,EAAE;wBACxC,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;qBAC9E;oBAED;;;;;uBAKG;oBACH,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;oBAEpF,SAAS,GAAG,KAAK,CAAC,QAAQ,GAAG,gBAAgB,CAAC;iBAC9C;aACD;YAED,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;gBAC1C,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,SAAS,EAAE;oBAC7C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;iBACxC;qBAAM;oBACN,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC,CAAC;iBACtD;aACD;iBAAM;gBACN,MAAM,IAAI,gBAAgB,CAAC,yBAAyB,CAAC,CAAC;aACtD;YACD,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACxC,IAAI,CAAC,aAAa,GAAG,EAAE,SAAS,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC;YAEvD,OAAO,WAAW,CAAC;QACpB,CAAC;KAAA;IAED;;;;;OAKG;IAEK,oBAAoB,CAAC,IAAY;QACxC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACtC,MAAM,MAAM,GAAG,EAAE,CAAC;YAClB,IAAI,SAAS,GAAG,IAAI,CAAC;YACrB,IAAI,MAAM,GAAG,CAAC,CAAC;YACf,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;gBAC3B,IAAI,SAAS,GAAG,CAAC,EAAE;oBAClB,OAAO,MAAM,CAAC,IAAI,gBAAgB,CAAC,iDAAiD,CAAC,CAAC,CAAC;iBACvF;YACF,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,EAAE,GAAG,EAAE;gBAChC;;;mBAGG;gBACH,IAAI,KAAK,CAAC;gBACV,OAAO,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,IAAI,EAAE;oBACxE,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC;oBACvB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACnB,IAAI,SAAS,GAAG,CAAC,EAAE;wBAClB,SAAS,GAAG,IAAI,GAAG,MAAM,CAAC;qBAC1B;iBACD;gBAED,IAAI,MAAM,KAAK,IAAI,EAAE;oBACpB,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;iBACtC;gBAED,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;oBAC5C,OAAO,MAAM,CAAC,IAAI,gBAAgB,CAAC,8DAA8D,CAAC,CAAC,CAAC;iBACpG;YACF,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACJ,CAAC;CACD"}