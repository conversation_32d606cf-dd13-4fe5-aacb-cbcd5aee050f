{"version": 3, "file": "LargeFileUploadTask.js", "sourceRoot": "", "sources": ["../../../../src/tasks/LargeFileUploadTask.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;;AAEH;;GAEG;AAEH,OAAO,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AACvD,OAAO,EAAE,oBAAoB,EAAE,MAAM,yBAAyB,CAAC;AAE/D,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAE/C,OAAO,EAAE,KAAK,EAAE,MAAM,wBAAwB,CAAC;AAC/C,OAAO,EAAE,YAAY,EAAE,MAAM,+BAA+B,CAAC;AAiE7D;;;GAGG;AACH,MAAM,OAAO,mBAAmB;IA6D/B;;;;;;;;;OASG;IACH,YAAmB,MAAc,EAAE,IAAmB,EAAE,aAAqC,EAAE,UAAsC,EAAE;QAtEvI;;;WAGG;QACK,sBAAiB,GAAW,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;QAmEnD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACpB,MAAM,IAAI,gBAAgB,CAAC,iHAAiH,CAAC,CAAC;SAC9I;aAAM;YACN,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;SACjB;QACD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YACvB,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC;SAC3C;QAED,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,SAAS,GAAG,IAAI,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;IAC3D,CAAC;IAlDD;;;;;;;;;;OAUG;IACI,MAAM,CAAO,mBAAmB,CAAC,MAAc,EAAE,UAAkB,EAAE,OAAY,EAAE,UAA0C,EAAE;;YACrI,MAAM,OAAO,GAAG,MAAM,MAAM;iBAC1B,GAAG,CAAC,UAAU,CAAC;iBACf,OAAO,CAAC,OAAO,CAAC;iBAChB,IAAI,CAAC,OAAO,CAAC,CAAC;YAChB,MAAM,sBAAsB,GAA2B;gBACtD,GAAG,EAAE,OAAO,CAAC,SAAS;gBACtB,MAAM,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC;gBAC5C,WAAW,EAAE,KAAK;aAClB,CAAC;YACF,OAAO,sBAAsB,CAAC;QAC/B,CAAC;KAAA;IA8BD;;;;;OAKG;IACK,UAAU,CAAC,MAAgB;QAClC,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAC3B,IAAI,OAAO,QAAQ,KAAK,WAAW,IAAI,QAAQ,KAAK,EAAE,EAAE;YACvD,OAAO,IAAI,KAAK,EAAE,CAAC;SACnB;QACD,MAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACvC,MAAM,MAAM,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC3C,IAAI,MAAM,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACzC,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;YACzB,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;SAC5B;QACD,OAAO,IAAI,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAClC,CAAC;IAED;;;;;OAKG;IACK,gBAAgB,CAAC,QAA8B;QACtD,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;QAClE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;IAC/D,CAAC;IAED;;;;OAIG;IACI,YAAY;QAClB,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,CAAC,CAAC,EAAE;YACnC,OAAO,IAAI,CAAC,SAAS,CAAC;SACtB;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;QACvC,IAAI,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC;QACnD,IAAI,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YAC/B,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;SAC9B;QACD,OAAO,IAAI,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IACpC,CAAC;IAED;;;;;;OAMG;IACI,SAAS,CAAC,KAAY;QAC5B,OAAO,CAAC,IAAI,CAAC,2GAA2G,CAAC,CAAC;QAC1H,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,YAAY,WAAW,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,YAAY,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,YAAY,UAAU,EAAE;YAC7H,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;SACnE;QACD,MAAM,IAAI,gBAAgB,CAAC,kKAAkK,CAAC,CAAC;IAChM,CAAC;IAED;;;;;OAKG;IACU,MAAM;;YAClB,MAAM,mBAAmB,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC;YAC7E,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE;gBACvC,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtC,IAAI,SAAS,CAAC,QAAQ,KAAK,CAAC,CAAC,EAAE;oBAC9B,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,oGAAoG,CAAC,CAAC;oBAC5H,GAAG,CAAC,IAAI,GAAG,iBAAiB,CAAC;oBAC7B,MAAM,GAAG,CAAC;iBACV;gBACD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;gBACvD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC/F,IAAI,CAAC,WAAW,EAAE;oBACjB,MAAM,IAAI,gBAAgB,CAAC,iEAAiE,CAAC,CAAC;iBAC9F;gBAED,MAAM,YAAY,GAAG,MAAM,oBAAoB,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;gBACzE;;;mBAGG;gBACH,IAAI,WAAW,CAAC,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,GAAG,IAAI,YAAY,CAAC,EAAE,CAAC,EAAE;oBAClF,MAAM,YAAY,GAAG,YAAY,CAAC,kBAAkB,CAAC,YAAY,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC;oBACxF,OAAO,YAAY,CAAC;iBACpB;gBAED;;mBAEG;gBACH,MAAM,GAAG,GAAyB;oBACjC,kBAAkB,EAAE,YAAY,CAAC,kBAAkB,IAAI,YAAY,CAAC,kBAAkB;oBACtF,kBAAkB,EAAE,YAAY,CAAC,kBAAkB,IAAI,YAAY,CAAC,kBAAkB;iBACtF,CAAC;gBACF,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;gBAC3B,IAAI,mBAAmB,IAAI,mBAAmB,CAAC,QAAQ,EAAE;oBACxD,mBAAmB,CAAC,QAAQ,CAAC,SAAS,EAAE,mBAAmB,CAAC,kBAAkB,CAAC,CAAC;iBAChF;aACD;QACF,CAAC;KAAA;IAED;;;;;;;;OAQG;IACU,WAAW,CAAC,SAAoC,EAAE,KAAY,EAAE,SAAiB;;YAC7F,OAAO,MAAM,IAAI,CAAC,MAAM;iBACtB,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;iBAC3B,OAAO,CAAC;gBACR,gBAAgB,EAAE,GAAG,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,GAAG,CAAC,EAAE;gBAC1D,eAAe,EAAE,SAAS,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,IAAI,SAAS,EAAE;gBACzE,cAAc,EAAE,0BAA0B;aAC1C,CAAC;iBACD,GAAG,CAAC,SAAS,CAAC,CAAC;QAClB,CAAC;KAAA;IAED;;;;;;;;OAQG;IACU,yBAAyB,CAAC,SAAkB,EAAE,KAAY,EAAE,SAAiB;;YACzF,OAAO,MAAM,IAAI,CAAC,MAAM;iBACtB,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;iBAC3B,OAAO,CAAC;gBACR,gBAAgB,EAAE,GAAG,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,GAAG,CAAC,EAAE;gBAC1D,eAAe,EAAE,SAAS,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,IAAI,SAAS,EAAE;gBACzE,cAAc,EAAE,0BAA0B;aAC1C,CAAC;iBACD,YAAY,CAAC,YAAY,CAAC,GAAG,CAAC;iBAC9B,GAAG,CAAC,SAAS,CAAC,CAAC;QAClB,CAAC;KAAA;IAED;;;;;OAKG;IACU,MAAM;;YAClB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,MAAM;iBACtC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;iBAC3B,YAAY,CAAC,YAAY,CAAC,GAAG,CAAC;iBAC9B,MAAM,EAAE,CAAC;YACX,IAAI,cAAc,CAAC,MAAM,KAAK,GAAG,EAAE;gBAClC,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,IAAI,CAAC;aACtC;YACD,OAAO,cAAc,CAAC;QACvB,CAAC;KAAA;IAED;;;;;OAKG;IACU,SAAS;;YACrB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;YACrE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAChC,OAAO,QAAQ,CAAC;QACjB,CAAC;KAAA;IAED;;;;;OAKG;IACU,MAAM;;YAClB,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;YACvB,OAAO,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;QAC5B,CAAC;KAAA;IAED;;;;;OAKG;IACI,gBAAgB;QACtB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC3B,CAAC;CACD"}