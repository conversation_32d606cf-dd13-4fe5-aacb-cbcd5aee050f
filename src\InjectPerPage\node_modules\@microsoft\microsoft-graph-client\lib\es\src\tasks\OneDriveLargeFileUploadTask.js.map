{"version": 3, "file": "OneDriveLargeFileUploadTask.js", "sourceRoot": "", "sources": ["../../../../src/tasks/OneDriveLargeFileUploadTask.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;;AAEH;;GAEG;AAEH,OAAO,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AAEvD,OAAO,EAAE,UAAU,EAAE,MAAM,+CAA+C,CAAC;AAE3E,OAAO,EAAsC,mBAAmB,EAA8B,MAAM,uBAAuB,CAAC;AAC5H,OAAO,EAAE,iBAAiB,EAAE,MAAM,mCAAmC,CAAC;AA6CtE;;;GAGG;AACH,MAAM,OAAO,2BAA+B,SAAQ,mBAAsB;IAqIzE;;;;;;;;;OASG;IACH,YAAmB,MAAc,EAAE,IAAmB,EAAE,aAAqC,EAAE,OAAmC;QACjI,KAAK,CAAC,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;IAC7C,CAAC;IAzID;;;;;;;OAOG;IACK,MAAM,CAAC,yBAAyB,CAAC,QAAgB,EAAE,OAAe,2BAA2B,CAAC,mBAAmB;QACxH,QAAQ,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC3B,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QACnB,IAAI,IAAI,KAAK,EAAE,EAAE;YAChB,IAAI,GAAG,GAAG,CAAC;SACX;QACD,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACpB,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;SAClB;QACD,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;YAClC,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC;SAClB;QACD,gGAAgG;QAChG,iGAAiG;QACjG,OAAO,kBAAkB,IAAI;aAC3B,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;aACjC,IAAI,CAAC,GAAG,CAAC,GAAG,kBAAkB,CAAC,QAAQ,CAAC,uBAAuB,CAAC;IACnE,CAAC;IAED;;;;;;;OAOG;IACK,MAAM,CAAC,WAAW,CAAC,IAA8B,EAAE,QAAgB;QAC1E,IAAI,OAAO,CAAC;QACZ,IAAI,IAAI,CAAC;QACT,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,IAAI,YAAY,IAAI,EAAE;YACxD,OAAO,GAAG,IAAI,IAAI,CAAC,CAAC,IAAY,CAAC,EAAE,QAAQ,CAAC,CAAC;YAC7C,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;SACpB;aAAM,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,IAAI,YAAY,IAAI,EAAE;YAC/D,OAAO,GAAG,IAAY,CAAC;YACvB,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;SACpB;aAAM,IAAI,OAAO,UAAU,KAAK,WAAW,IAAI,IAAI,YAAY,UAAU,EAAE;YAC3E,MAAM,CAAC,GAAG,IAAkB,CAAC;YAC7B,IAAI,GAAG,CAAC,CAAC,UAAU,CAAC;YACpB,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;SACpE;QACD,OAAO;YACN,OAAO;YACP,IAAI;SACJ,CAAC;IACH,CAAC;IAED;;;;;;;;;OASG;IACI,MAAM,CAAO,MAAM,CAAC,MAAc,EAAE,IAA8B,EAAE,OAAuC;;YACjH,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE;gBACjC,MAAM,IAAI,gBAAgB,CAAC,gGAAgG,CAAC,CAAC;aAC7H;YACD,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;YAClC,MAAM,QAAQ,GAAG,2BAA2B,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YACzE,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC1E,OAAO,IAAI,CAAC,wBAAwB,CAAkC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QACjG,CAAC;KAAA;IAED;;;;;;;;;OASG;IACI,MAAM,CAAO,wBAAwB,CAAI,MAAc,EAAE,UAAyB,EAAE,OAAuC;;YACjI,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE;gBACvC,MAAM,IAAI,gBAAgB,CAAC,wHAAwH,CAAC,CAAC;aACrJ;YACD,MAAM,UAAU,GAAG,2BAA2B,CAAC,yBAAyB,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;YACzG,MAAM,oBAAoB,GAAqC;gBAC9D,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,eAAe,EAAE,OAAO,CAAC,eAAe;gBACxC,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;aAC1C,CAAC;YACF,MAAM,OAAO,GAAG,MAAM,2BAA2B,CAAC,mBAAmB,CAAC,MAAM,EAAE,UAAU,EAAE,oBAAoB,CAAC,CAAC;YAChH,MAAM,SAAS,GAAG,iBAAiB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACvD,OAAO,IAAI,2BAA2B,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE;gBACnE,SAAS;gBACT,mBAAmB,EAAE,OAAO,CAAC,mBAAmB;aAChD,CAAC,CAAC;QACJ,CAAC;KAAA;IAED;;;;;;;;;OASG;IACI,MAAM,CAAO,mBAAmB,CAAC,MAAc,EAAE,UAAkB,EAAE,cAAgD;;;;;YAC3H,MAAM,OAAO,GAAG;gBACf,IAAI,EAAE;oBACL,mCAAmC,EAAE,CAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,gBAAgB,KAAI,QAAQ;oBACjF,IAAI,EAAE,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,QAAQ;oBAC9B,WAAW,EAAE,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,eAAe;iBAC5C;aACD,CAAC;YACF,OAAO,OAAM,mBAAmB,YAAC,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE;QAC/D,CAAC;KAAA;IAgBD;;;;;;OAMG;IACU,MAAM,CAAC,UAAkB,EAAE,gBAAgB,GAAG,QAAQ;;YAClE,MAAM,OAAO,GAAG;gBACf,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI;gBACpB,mCAAmC,EAAE,gBAAgB;gBACrD,4BAA4B,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG;aACpD,CAAC;YACF,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACvD,CAAC;KAAA;;AAhKD;;;;GAIG;AACY,+CAAmB,GAAG,GAAG,CAAC"}