/**
* -------------------------------------------------------------------------------------------
* Copyright (c) Microsoft Corporation.  All Rights Reserved.  Licensed under the MIT License.
* See License in the project root for license information.
* -------------------------------------------------------------------------------------------
*/
var MicrosoftGraph=function(e){"use strict";function t(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function r(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function n(e,t,n){return t&&r(e.prototype,t),n&&r(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}var i={exports:{}};!function(e){var t=function(e){var t,r=Object.prototype,n=r.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",o=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function c(e,t,r,n){var i=t&&t.prototype instanceof y?t:y,a=Object.create(i.prototype),o=new S(n||[]);return a._invoke=function(e,t,r){var n=d;return function(i,a){if(n===h)throw new Error("Generator is already running");if(n===p){if("throw"===i)throw a;return C()}for(r.method=i,r.arg=a;;){var o=r.delegate;if(o){var s=O(o,r);if(s){if(s===v)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(n===d)throw n=p,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n=h;var u=l(e,t,r);if("normal"===u.type){if(n=r.done?p:f,u.arg===v)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n=p,r.method="throw",r.arg=u.arg)}}}(e,r,o),a}function l(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=c;var d="suspendedStart",f="suspendedYield",h="executing",p="completed",v={};function y(){}function m(){}function g(){}var w={};u(w,a,(function(){return this}));var x=Object.getPrototypeOf,b=x&&x(x(P([])));b&&b!==r&&n.call(b,a)&&(w=b);var E=g.prototype=y.prototype=Object.create(w);function k(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function R(e,t){function r(i,a,o,s){var u=l(e[i],e,a);if("throw"!==u.type){var c=u.arg,d=c.value;return d&&"object"==typeof d&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,o,s)}),(function(e){r("throw",e,o,s)})):t.resolve(d).then((function(e){c.value=e,o(c)}),(function(e){return r("throw",e,o,s)}))}s(u.arg)}var i;this._invoke=function(e,n){function a(){return new t((function(t,i){r(e,n,t,i)}))}return i=i?i.then(a,a):a()}}function O(e,r){var n=e.iterator[r.method];if(n===t){if(r.delegate=null,"throw"===r.method){if(e.iterator.return&&(r.method="return",r.arg=t,O(e,r),"throw"===r.method))return v;r.method="throw",r.arg=new TypeError("The iterator does not provide a 'throw' method")}return v}var i=l(n,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function A(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function T(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function S(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(A,this),this.reset(!0)}function P(e){if(e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function r(){for(;++i<e.length;)if(n.call(e,i))return r.value=e[i],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}return{next:C}}function C(){return{value:t,done:!0}}return m.prototype=g,u(E,"constructor",g),u(g,"constructor",m),m.displayName=u(g,s,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===m||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,g):(e.__proto__=g,u(e,s,"GeneratorFunction")),e.prototype=Object.create(E),e},e.awrap=function(e){return{__await:e}},k(R.prototype),u(R.prototype,o,(function(){return this})),e.AsyncIterator=R,e.async=function(t,r,n,i,a){void 0===a&&(a=Promise);var o=new R(c(t,r,n,i),a);return e.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},k(E),u(E,s,"Generator"),u(E,a,(function(){return this})),u(E,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function r(){for(;t.length;){var n=t.pop();if(n in e)return r.value=n,r.done=!1,r}return r.done=!0,r}},e.values=P,S.prototype={constructor:S,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(T),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function i(n,i){return s.type="throw",s.arg=e,r.next=n,i&&(r.method="next",r.arg=t),!!i}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],s=o.completion;if("root"===o.tryLoc)return i("end");if(o.tryLoc<=this.prev){var u=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(u&&c){if(this.prev<o.catchLoc)return i(o.catchLoc,!0);if(this.prev<o.finallyLoc)return i(o.finallyLoc)}else if(u){if(this.prev<o.catchLoc)return i(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return i(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=e,o.arg=t,a?(this.method="next",this.next=a.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),T(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;T(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:P(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}(e.exports);try{regeneratorRuntime=t}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=t:Function("r","regeneratorRuntime = r")(t)}}(i);var a,o=i.exports;function s(e,t,r,n){return new(r||(r=Promise))((function(i,a){function o(e){try{u(n.next(e))}catch(e){a(e)}}function s(e){try{u(n.throw(e))}catch(e){a(e)}}function u(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,s)}u((n=n.apply(e,t||[])).next())}))}function u(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(!e)return;if("string"==typeof e)return c(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return c(e,t)}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return o=e.done,e},e:function(e){s=!0,a=e},f:function(){try{o||null==r.return||r.return()}finally{if(s)throw a}}}}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}!function(e){e.GET="GET",e.PATCH="PATCH",e.POST="POST",e.PUT="PUT",e.DELETE="DELETE"}(a||(a={}));var l=function(){function e(r){if(t(this,e),this.requests=new Map,void 0!==r){var n=e.requestLimit;if(r.length>n){var i=new Error("Maximum requests limit exceeded, Max allowed number of requests are ".concat(n));throw i.name="Limit Exceeded Error",i}var a,o=u(r);try{for(o.s();!(a=o.n()).done;){var s=a.value;this.addRequest(s)}}catch(e){o.e(e)}finally{o.f()}}}return n(e,[{key:"addRequest",value:function(t){var r=e.requestLimit;if(""===t.id){var n=new Error("Id for a request is empty, Please provide an unique id");throw n.name="Empty Id For Request",n}if(this.requests.size===r){var i=new Error("Maximum requests limit exceeded, Max allowed number of requests are ".concat(r));throw i.name="Limit Exceeded Error",i}if(this.requests.has(t.id)){var a=new Error("Adding request with duplicate id ".concat(t.id,", Make the id of the requests unique"));throw a.name="Duplicate RequestId Error",a}return this.requests.set(t.id,t),t.id}},{key:"removeRequest",value:function(e){for(var t=this.requests.delete(e),r=this.requests.entries(),n=r.next();!n.done;){var i=n.value[1].dependsOn;if(void 0!==i){var a=i.indexOf(e);-1!==a&&i.splice(a,1),0===i.length&&delete n.value[1].dependsOn}n=r.next()}return t}},{key:"getContent",value:function(){return s(this,void 0,void 0,o.mark((function t(){var r,n,i,a,s,u,c,l,d;return o.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n={requests:r=[]},i=this.requests.entries(),!(a=i.next()).done){t.next=8;break}throw(s=new Error("No requests added yet, Please add at least one request.")).name="Empty Payload",s;case 8:if(e.validateDependencies(this.requests)){t.next=12;break}throw(u=new Error("Invalid dependency found, Dependency should be:\n1. Parallel - no individual request states a dependency in the dependsOn property.\n2. Serial - all individual requests depend on the previous individual request.\n3. Same - all individual requests that state a dependency in the dependsOn property, state the same dependency.")).name="Invalid Dependency",u;case 12:if(a.done){t.next=27;break}return c=a.value[1],t.next=16,e.getRequestData(c.request);case 16:if(void 0===(l=t.sent).body||void 0!==l.headers&&void 0!==l.headers["content-type"]){t.next=21;break}throw(d=new Error("Content-type header is not mentioned for request #".concat(c.id,", For request having body, Content-type header should be mentioned"))).name="Invalid Content-type header",d;case 21:l.id=c.id,void 0!==c.dependsOn&&c.dependsOn.length>0&&(l.dependsOn=c.dependsOn),r.push(l),a=i.next(),t.next=12;break;case 27:return n.requests=r,t.abrupt("return",n);case 29:case"end":return t.stop()}}),t,this)})))}},{key:"addDependency",value:function(e,t){if(!this.requests.has(e)){var r=new Error("Dependent ".concat(e," does not exists, Please check the id"));throw r.name="Invalid Dependent",r}if(void 0!==t&&!this.requests.has(t)){var n=new Error("Dependency ".concat(t," does not exists, Please check the id"));throw n.name="Invalid Dependency",n}if(void 0!==t){var i=this.requests.get(e);if(void 0===i.dependsOn&&(i.dependsOn=[]),-1!==i.dependsOn.indexOf(t)){var a=new Error("Dependency ".concat(t," is already added for the request ").concat(e));throw a.name="Duplicate Dependency",a}i.dependsOn.push(t)}else{for(var o,s=this.requests.entries(),u=s.next();!u.done&&u.value[1].id!==e;)o=u,u=s.next();if(void 0===o){var c=new Error("Can't add dependency ".concat(t,", There is only a dependent request in the batch"));throw c.name="Invalid Dependency Addition",c}var l=o.value[0];if(void 0===u.value[1].dependsOn&&(u.value[1].dependsOn=[]),-1!==u.value[1].dependsOn.indexOf(l)){var d=new Error("Dependency ".concat(l," is already added for the request ").concat(e));throw d.name="Duplicate Dependency",d}u.value[1].dependsOn.push(l)}}},{key:"removeDependency",value:function(e,t){var r=this.requests.get(e);if(void 0===r||void 0===r.dependsOn||0===r.dependsOn.length)return!1;if(void 0!==t){var n=r.dependsOn.indexOf(t);return-1!==n&&(r.dependsOn.splice(n,1),!0)}return delete r.dependsOn,!0}}],[{key:"validateDependencies",value:function(e){if(0===e.size){var t=new Error("Empty requests map, Please provide at least one request.");throw t.name="Empty Requests Error",t}return function(e){for(var t=e.entries(),r=t.next();!r.done;){var n=r.value[1];if(void 0!==n.dependsOn&&n.dependsOn.length>0)return!1;r=t.next()}return!0}(e)||function(e){var t=e.entries(),r=t.next(),n=r.value[1];if(void 0!==n.dependsOn&&n.dependsOn.length>0)return!1;var i=r;for(r=t.next();!r.done;){var a=r.value[1];if(void 0===a.dependsOn||1!==a.dependsOn.length||a.dependsOn[0]!==i.value[1].id)return!1;i=r,r=t.next()}return!0}(e)||function(e){var t,r=e.entries(),n=r.next(),i=n.value[1];if(void 0===i.dependsOn||0===i.dependsOn.length)t=i.id;else{if(1!==i.dependsOn.length)return!1;var a=i.dependsOn[0];if(a===i.id||!e.has(a))return!1;t=a}for(n=r.next();!n.done;){var o=n.value[1];if((void 0===o.dependsOn||0===o.dependsOn.length)&&t!==o.id)return!1;if(void 0!==o.dependsOn&&0!==o.dependsOn.length){if(1===o.dependsOn.length&&(o.id===t||o.dependsOn[0]!==t))return!1;if(o.dependsOn.length>1)return!1}n=r.next()}return!0}(e)}},{key:"getRequestData",value:function(t){return s(this,void 0,void 0,o.mark((function r(){var n,i,s;return o.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(n={url:""},i=new RegExp("^https?://"),n.url=i.test(t.url)?"/"+t.url.split(/.*?\/\/.*?\//)[1]:t.url,n.method=t.method,s={},t.headers.forEach((function(e,t){s[t]=e})),Object.keys(s).length&&(n.headers=s),t.method!==a.PATCH&&t.method!==a.POST&&t.method!==a.PUT){r.next=11;break}return r.next=10,e.getRequestBody(t);case 10:n.body=r.sent;case 11:return r.abrupt("return",n);case 12:case"end":return r.stop()}}),r)})))}},{key:"getRequestBody",value:function(e){return s(this,void 0,void 0,o.mark((function t(){var r,n,i,a,s,u;return o.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=!1,t.prev=1,i=e.clone(),t.next=5,i.json();case 5:n=t.sent,r=!0,t.next=11;break;case 9:t.prev=9,t.t0=t.catch(1);case 11:if(r){t.next=33;break}if(t.prev=12,"undefined"==typeof Blob){t.next=23;break}return t.next=16,e.blob();case 16:return a=t.sent,s=new FileReader,t.next=20,new Promise((function(e){s.addEventListener("load",(function(){var t=s.result,r=new RegExp("^s*data:(.+?/.+?(;.+?=.+?)*)?(;base64)?,(.*)s*$").exec(t);e(r[4])}),!1),s.readAsDataURL(a)}));case 20:n=t.sent,t.next=28;break;case 23:if("undefined"==typeof Buffer){t.next=28;break}return t.next=26,e.buffer();case 26:u=t.sent,n=u.toString("base64");case 28:r=!0,t.next=33;break;case 31:t.prev=31,t.t1=t.catch(12);case 33:return t.abrupt("return",n);case 34:case"end":return t.stop()}}),t,null,[[1,9],[12,31]])})))}}]),e}();l.requestLimit=20;var d=function(){function e(r){t(this,e),this.responses=new Map,this.update(r)}return n(e,[{key:"createResponseObject",value:function(e){var t=e.body,r={};if(r.status=e.status,void 0!==e.statusText&&(r.statusText=e.statusText),r.headers=e.headers,void 0!==r.headers&&void 0!==r.headers["Content-Type"]&&"application/json"===r.headers["Content-Type"].split(";")[0]){var n=JSON.stringify(t);return new Response(n,r)}return new Response(t,r)}},{key:"update",value:function(e){this.nextLink=e["@odata.nextLink"];for(var t=e.responses,r=0,n=t.length;r<n;r++)this.responses.set(t[r].id,this.createResponseObject(t[r]))}},{key:"getResponseById",value:function(e){return this.responses.get(e)}},{key:"getResponses",value:function(){return this.responses}},{key:"getResponsesIterator",value:o.mark((function e(){var t,r;return o.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=this.responses.entries(),r=t.next();case 2:if(r.done){e.next=8;break}return e.next=5,r.value;case 5:r=t.next(),e.next=2;break;case 8:case"end":return e.stop()}}),e,this)}))}]),e}(),f=new Set(["graph.microsoft.com","graph.microsoft.us","dod-graph.microsoft.us","graph.microsoft.de","microsoftgraph.chinacloudapi.cn","canary.graph.microsoft.com"]);function h(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function p(e,t){return(p=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function v(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&p(e,t)}function y(e){return(y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function m(e,t){if(t&&("object"===y(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return h(e)}function g(e){return(g=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function w(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function x(e,t,r){return(x=w()?Reflect.construct:function(e,t,r){var n=[null];n.push.apply(n,t);var i=new(Function.bind.apply(e,n));return r&&p(i,r.prototype),i}).apply(null,arguments)}function b(e){var t="function"==typeof Map?new Map:void 0;return(b=function(e){if(null===e||(r=e,-1===Function.toString.call(r).indexOf("[native code]")))return e;var r;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,n)}function n(){return x(e,arguments,g(this).constructor)}return n.prototype=Object.create(e.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),p(n,e)})(e)}function E(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=g(e);if(t){var i=g(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return m(this,r)}}var k=function(e){v(i,e);var r=E(i);function i(e){var n;return t(this,i),n=r.call(this,e),Object.setPrototypeOf(h(n),i.prototype),n}return n(i,null,[{key:"setGraphClientError",value:function(e){var t;return e instanceof Error?t=e:(t=new i).customError=e,t}}]),i}(b(Error)),R=["$select","$expand","$orderby","$filter","$top","$skip","$skipToken","$count"],O=function(e){var t=e&&e.constructor&&e.constructor.name;if("Buffer"===t||"Blob"===t||"File"===t||"FormData"===t||"string"==typeof e)return e;if("ArrayBuffer"===t)e=Buffer.from(e);else if("Int8Array"===t||"Int16Array"===t||"Int32Array"===t||"Uint8Array"===t||"Uint16Array"===t||"Uint32Array"===t||"Uint8ClampedArray"===t||"Float32Array"===t||"Float64Array"===t||"DataView"===t)e=Buffer.from(e.buffer);else try{e=JSON.stringify(e)}catch(e){throw new Error("Unable to stringify the content")}return e},A=function(e){return S(e)},T=function(e,t){return t.forEach((function(e){return P(e)})),S(e,t)},S=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:f;if(-1!==(e=e.toLowerCase()).indexOf("https://")){var r=(e=e.replace("https://","")).indexOf(":"),n=e.indexOf("/"),i="";if(-1!==n)return-1!==r&&r<n?(i=e.substring(0,r),t.has(i)):(i=e.substring(0,n),t.has(i))}return!1},P=function(e){if(-1!==e.indexOf("/"))throw new k("Please add only hosts or hostnames to the CustomHosts config. If the url is `http://example.com:3000/`, host is `example:3000`")};function C(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(!e)return;if("string"==typeof e)return D(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return D(e,t)}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return o=e.done,e},e:function(e){s=!0,a=e},f:function(){try{o||null==r.return||r.return()}finally{if(s)throw a}}}}function D(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var L=function(){function e(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];t(this,e),this.middlewareOptions=new Map;var n,i=C(r);try{for(i.s();!(n=i.n()).done;){var a=n.value,o=a.constructor;this.middlewareOptions.set(o,a)}}catch(e){i.e(e)}finally{i.f()}}return n(e,[{key:"getMiddlewareOptions",value:function(e){return this.middlewareOptions.get(e)}},{key:"setMiddlewareOptions",value:function(e,t){this.middlewareOptions.set(e,t)}}]),e}();function _(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var M,U=function(){for(var e="",t=0;t<32;t++)8!==t&&12!==t&&16!==t&&20!==t||(e+="-"),e+=Math.floor(16*Math.random()).toString(16);return e},N=function(e,t,r){var n=null;if("undefined"!=typeof Request&&e instanceof Request)n=e.headers.get(r);else if(void 0!==t&&void 0!==t.headers)if("undefined"!=typeof Headers&&t.headers instanceof Headers)n=t.headers.get(r);else if(t.headers instanceof Array){for(var i=t.headers,a=0,o=i.length;a<o;a++)if(i[a][0]===r){n=i[a][1];break}}else void 0!==t.headers[r]&&(n=t.headers[r]);return n},q=function(e,t,r,n){if("undefined"!=typeof Request&&e instanceof Request)e.headers.set(r,n);else if(void 0!==t)if(void 0===t.headers)t.headers=new Headers(_({},r,n));else if("undefined"!=typeof Headers&&t.headers instanceof Headers)t.headers.set(r,n);else if(t.headers instanceof Array){for(var i=0,a=t.headers.length;i<a;i++){var o=t.headers[i];if(o[0]===r){o[1]=n;break}}i===a&&t.headers.push([r,n])}else Object.assign(t.headers,_({},r,n))},I=function(e,t,r,n){"undefined"!=typeof Request&&e instanceof Request?e.headers.append(r,n):void 0!==t&&(void 0===t.headers?t.headers=new Headers(_({},r,n)):"undefined"!=typeof Headers&&t.headers instanceof Headers?t.headers.append(r,n):t.headers instanceof Array?t.headers.push([r,n]):void 0===t.headers?t.headers=_({},r,n):void 0===t.headers[r]?t.headers[r]=n:t.headers[r]+=", ".concat(n))},F=function(e,t){return s(void 0,void 0,void 0,o.mark((function r(){var n,i,a,s,u,c,l,d,f,h,p,v;return o.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(!t.headers.get("Content-Type")){r.next=6;break}return r.next=3,t.blob();case 3:r.t0=r.sent,r.next=9;break;case 6:return r.next=8,Promise.resolve(void 0);case 8:r.t0=r.sent;case 9:return n=r.t0,i=t.method,a=t.headers,s=t.referrer,u=t.referrerPolicy,c=t.mode,l=t.credentials,d=t.cache,f=t.redirect,h=t.integrity,p=t.keepalive,v=t.signal,r.abrupt("return",new Request(e,{method:i,headers:a,body:n,referrer:s,referrerPolicy:u,mode:c,credentials:l,cache:d,redirect:f,integrity:h,keepalive:p,signal:v}));case 12:case"end":return r.stop()}}),r)})))},H=n((function e(r,n){t(this,e),this.authenticationProvider=r,this.authenticationProviderOptions=n}));e.FeatureUsageFlag=void 0,(M=e.FeatureUsageFlag||(e.FeatureUsageFlag={}))[M.NONE=0]="NONE",M[M.REDIRECT_HANDLER_ENABLED=1]="REDIRECT_HANDLER_ENABLED",M[M.RETRY_HANDLER_ENABLED=2]="RETRY_HANDLER_ENABLED",M[M.AUTHENTICATION_HANDLER_ENABLED=4]="AUTHENTICATION_HANDLER_ENABLED";var j=function(){function r(){t(this,r),this.featureUsage=e.FeatureUsageFlag.NONE}return n(r,[{key:"setFeatureUsage",value:function(e){this.featureUsage=this.featureUsage|e}},{key:"getFeatureUsage",value:function(){return this.featureUsage.toString(16)}}],[{key:"updateFeatureUsageFlag",value:function(e,t){var n;e.middlewareControl instanceof L?n=e.middlewareControl.getMiddlewareOptions(r):e.middlewareControl=new L,void 0===n&&(n=new r,e.middlewareControl.setMiddlewareOptions(r,n)),n.setFeatureUsage(t)}}]),r}(),B=function(){function r(e){t(this,r),this.authenticationProvider=e}return n(r,[{key:"execute",value:function(t){return s(this,void 0,void 0,o.mark((function n(){var i,a,s,u,c,l;return o.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(i="string"==typeof t.request?t.request:t.request.url,!(A(i)||t.customHosts&&T(i,t.customHosts))){n.next=13;break}return t.middlewareControl instanceof L&&(a=t.middlewareControl.getMiddlewareOptions(H)),a&&(s=a.authenticationProvider,u=a.authenticationProviderOptions),s||(s=this.authenticationProvider),n.next=7,s.getAccessToken(u);case 7:c=n.sent,l="Bearer ".concat(c),I(t.request,t.options,r.AUTHORIZATION_HEADER,l),j.updateFeatureUsageFlag(t,e.FeatureUsageFlag.AUTHENTICATION_HANDLER_ENABLED),n.next=14;break;case 13:t.options.headers&&delete t.options.headers[r.AUTHORIZATION_HEADER];case 14:return n.next=16,this.nextMiddleware.execute(t);case 16:return n.abrupt("return",n.sent);case 17:case"end":return n.stop()}}),n,this)})))}},{key:"setNext",value:function(e){this.nextMiddleware=e}}]),r}();B.AUTHORIZATION_HEADER="Authorization";var Q=function(){function e(){t(this,e)}return n(e,[{key:"execute",value:function(e){return s(this,void 0,void 0,o.mark((function t(){return o.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,fetch(e.request,e.options);case 2:e.response=t.sent;case 3:case"end":return t.stop()}}),t)})))}}]),e}(),V=function(){function e(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e.DEFAULT_DELAY,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.DEFAULT_MAX_RETRIES,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.defaultShouldRetry;if(t(this,e),r>e.MAX_DELAY&&n>e.MAX_MAX_RETRIES){var a=new Error("Delay and MaxRetries should not be more than ".concat(e.MAX_DELAY," and ").concat(e.MAX_MAX_RETRIES));throw a.name="MaxLimitExceeded",a}if(r>e.MAX_DELAY){var o=new Error("Delay should not be more than ".concat(e.MAX_DELAY));throw o.name="MaxLimitExceeded",o}if(n>e.MAX_MAX_RETRIES){var s=new Error("MaxRetries should not be more than ".concat(e.MAX_MAX_RETRIES));throw s.name="MaxLimitExceeded",s}if(r<0&&n<0){var u=new Error("Delay and MaxRetries should not be negative");throw u.name="MinExpectationNotMet",u}if(r<0){var c=new Error("Delay should not be negative");throw c.name="MinExpectationNotMet",c}if(n<0){var l=new Error("MaxRetries should not be negative");throw l.name="MinExpectationNotMet",l}this.delay=Math.min(r,e.MAX_DELAY),this.maxRetries=Math.min(n,e.MAX_MAX_RETRIES),this.shouldRetry=i}return n(e,[{key:"getMaxDelay",value:function(){return e.MAX_DELAY}}]),e}();V.DEFAULT_DELAY=3,V.DEFAULT_MAX_RETRIES=3,V.MAX_DELAY=180,V.MAX_MAX_RETRIES=10,V.defaultShouldRetry=function(){return!0};var X=function(){function r(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new V;t(this,r),this.options=e}return n(r,[{key:"isRetry",value:function(e){return-1!==r.RETRY_STATUS_CODES.indexOf(e.status)}},{key:"isBuffered",value:function(e,t){var r="string"==typeof e?t.method:e.method;if((r===a.PUT||r===a.PATCH||r===a.POST)&&"application/octet-stream"===N(e,t,"Content-Type"))return!1;return!0}},{key:"getDelay",value:function(e,t,n){var i,a=function(){return Number(Math.random().toFixed(3))},o=void 0!==e.headers?e.headers.get(r.RETRY_AFTER_HEADER):null;return i=null!==o?Number.isNaN(Number(o))?Math.round((new Date(o).getTime()-Date.now())/1e3):Number(o):t>=2?this.getExponentialBackOffTime(t)+n+a():n+a(),Math.min(i,this.options.getMaxDelay()+a())}},{key:"getExponentialBackOffTime",value:function(e){return Math.round(.5*(Math.pow(2,e)-1))}},{key:"sleep",value:function(e){return s(this,void 0,void 0,o.mark((function t(){var r;return o.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=1e3*e,t.abrupt("return",new Promise((function(e){return setTimeout(e,r)})));case 2:case"end":return t.stop()}}),t)})))}},{key:"getOptions",value:function(e){var t;return e.middlewareControl instanceof L&&(t=e.middlewareControl.getMiddlewareOptions(this.options.constructor)),void 0===t&&(t=Object.assign(new V,this.options)),t}},{key:"executeWithRetry",value:function(e,t,n){return s(this,void 0,void 0,o.mark((function i(){var a;return o.wrap((function(i){for(;;)switch(i.prev=i.next){case 0:return i.next=2,this.nextMiddleware.execute(e);case 2:if(!(t<n.maxRetries&&this.isRetry(e.response)&&this.isBuffered(e.request,e.options)&&n.shouldRetry(n.delay,t,e.request,e.options,e.response))){i.next=13;break}return++t,q(e.request,e.options,r.RETRY_ATTEMPT_HEADER,t.toString()),a=this.getDelay(e.response,t,n.delay),i.next=8,this.sleep(a);case 8:return i.next=10,this.executeWithRetry(e,t,n);case 10:return i.abrupt("return",i.sent);case 13:return i.abrupt("return");case 14:case"end":return i.stop()}}),i,this)})))}},{key:"execute",value:function(t){return s(this,void 0,void 0,o.mark((function r(){var n;return o.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return 0,n=this.getOptions(t),j.updateFeatureUsageFlag(t,e.FeatureUsageFlag.RETRY_HANDLER_ENABLED),r.next=5,this.executeWithRetry(t,0,n);case 5:return r.abrupt("return",r.sent);case 6:case"end":return r.stop()}}),r,this)})))}},{key:"setNext",value:function(e){this.nextMiddleware=e}}]),r}();X.RETRY_STATUS_CODES=[429,503,504],X.RETRY_ATTEMPT_HEADER="Retry-Attempt",X.RETRY_AFTER_HEADER="Retry-After";var G=n((function e(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e.DEFAULT_MAX_REDIRECTS,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.defaultShouldRedirect;if(t(this,e),r>e.MAX_MAX_REDIRECTS){var i=new Error("MaxRedirects should not be more than ".concat(e.MAX_MAX_REDIRECTS));throw i.name="MaxLimitExceeded",i}if(r<0){var a=new Error("MaxRedirects should not be negative");throw a.name="MinExpectationNotMet",a}this.maxRedirects=r,this.shouldRedirect=n}));G.DEFAULT_MAX_REDIRECTS=5,G.MAX_MAX_REDIRECTS=20,G.defaultShouldRedirect=function(){return!0};var $=function(){function r(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new G;t(this,r),this.options=e}return n(r,[{key:"isRedirect",value:function(e){return-1!==r.REDIRECT_STATUS_CODES.indexOf(e.status)}},{key:"hasLocationHeader",value:function(e){return e.headers.has(r.LOCATION_HEADER)}},{key:"getLocationHeader",value:function(e){return e.headers.get(r.LOCATION_HEADER)}},{key:"isRelativeURL",value:function(e){return-1===e.indexOf("://")}},{key:"shouldDropAuthorizationHeader",value:function(e,t){var r,n,i=/^[A-Za-z].+?:\/\/.+?(?=\/|$)/,a=i.exec(e);null!==a&&(r=a[0]);var o=i.exec(t);return null!==o&&(n=o[0]),void 0!==r&&void 0!==n&&r!==n}},{key:"updateRequestUrl",value:function(e,t){return s(this,void 0,void 0,o.mark((function r(){return o.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if("string"!=typeof t.request){r.next=4;break}r.t0=e,r.next=7;break;case 4:return r.next=6,F(e,t.request);case 6:r.t0=r.sent;case 7:t.request=r.t0;case 8:case"end":return r.stop()}}),r)})))}},{key:"getOptions",value:function(e){var t;return e.middlewareControl instanceof L&&(t=e.middlewareControl.getMiddlewareOptions(G)),void 0===t&&(t=Object.assign(new G,this.options)),t}},{key:"executeWithRedirect",value:function(e,t,n){return s(this,void 0,void 0,o.mark((function i(){var s,u;return o.wrap((function(i){for(;;)switch(i.prev=i.next){case 0:return i.next=2,this.nextMiddleware.execute(e);case 2:if(s=e.response,!(t<n.maxRedirects&&this.isRedirect(s)&&this.hasLocationHeader(s)&&n.shouldRedirect(s))){i.next=18;break}if(++t,s.status!==r.STATUS_CODE_SEE_OTHER){i.next=10;break}e.options.method=a.GET,delete e.options.body,i.next=14;break;case 10:return u=this.getLocationHeader(s),!this.isRelativeURL(u)&&this.shouldDropAuthorizationHeader(s.url,u)&&delete e.options.headers[r.AUTHORIZATION_HEADER],i.next=14,this.updateRequestUrl(u,e);case 14:return i.next=16,this.executeWithRedirect(e,t,n);case 16:i.next=19;break;case 18:return i.abrupt("return");case 19:case"end":return i.stop()}}),i,this)})))}},{key:"execute",value:function(t){return s(this,void 0,void 0,o.mark((function n(){var i;return o.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return 0,i=this.getOptions(t),t.options.redirect=r.MANUAL_REDIRECT,j.updateFeatureUsageFlag(t,e.FeatureUsageFlag.REDIRECT_HANDLER_ENABLED),n.next=6,this.executeWithRedirect(t,0,i);case 6:return n.abrupt("return",n.sent);case 7:case"end":return n.stop()}}),n,this)})))}},{key:"setNext",value:function(e){this.nextMiddleware=e}}]),r}();$.REDIRECT_STATUS_CODES=[301,302,303,307,308],$.STATUS_CODE_SEE_OTHER=303,$.LOCATION_HEADER="Location",$.AUTHORIZATION_HEADER="Authorization",$.MANUAL_REDIRECT="manual";var z=function(){function e(){t(this,e)}return n(e,[{key:"execute",value:function(t){return s(this,void 0,void 0,o.mark((function r(){var n,i,a,s,u;return o.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return n="string"==typeof t.request?t.request:t.request.url,A(n)||t.customHosts&&T(n,t.customHosts)?((i=N(t.request,t.options,e.CLIENT_REQUEST_ID_HEADER))||(i=U(),q(t.request,t.options,e.CLIENT_REQUEST_ID_HEADER,i)),a="".concat(e.PRODUCT_NAME,"/").concat("3.0.2"),t.middlewareControl instanceof L&&(s=t.middlewareControl.getMiddlewareOptions(j)),s&&(u=s.getFeatureUsage(),a+=" (".concat(e.FEATURE_USAGE_STRING,"=").concat(u,")")),I(t.request,t.options,e.SDK_VERSION_HEADER,a)):(delete t.options.headers[e.CLIENT_REQUEST_ID_HEADER],delete t.options.headers[e.SDK_VERSION_HEADER]),r.next=4,this.nextMiddleware.execute(t);case 4:return r.abrupt("return",r.sent);case 5:case"end":return r.stop()}}),r,this)})))}},{key:"setNext",value:function(e){this.nextMiddleware=e}}]),e}();z.CLIENT_REQUEST_ID_HEADER="client-request-id",z.SDK_VERSION_HEADER="SdkVersion",z.PRODUCT_NAME="graph-js",z.FEATURE_USAGE_STRING="featureUsage";var Y,W=function(){function e(){t(this,e)}return n(e,null,[{key:"getDefaultMiddlewareChain",value:function(e){var t=[],r=new B(e),n=new X(new V),i=new z,a=new Q;if(t.push(r),t.push(n),"object"===("undefined"==typeof process?"undefined":y(process))&&"function"==typeof require){var o=new $(new G);t.push(o)}return t.push(i),t.push(a),t}}]),e}();e.ChaosStrategy=void 0,(Y=e.ChaosStrategy||(e.ChaosStrategy={}))[Y.MANUAL=0]="MANUAL",Y[Y.RANDOM=1]="RANDOM";var J,Z,K,ee,te=n((function r(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e.ChaosStrategy.RANDOM,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Some error Happened",a=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,s=arguments.length>4?arguments[4]:void 0,u=arguments.length>5?arguments[5]:void 0;if(t(this,r),this.chaosStrategy=n,this.statusCode=a,this.statusMessage=i,this.chaosPercentage=void 0!==o?o:10,this.responseBody=s,this.headers=u,this.chaosPercentage>100)throw new Error("Error Pecentage can not be more than 100")})),re={GET:[429,500,502,503,504],POST:[429,500,502,503,504,507],PUT:[429,500,502,503,504,507],PATCH:[429,500,502,503,504],DELETE:[429,500,502,503,504,507]},ne={100:"Continue",101:"Switching Protocols",102:"Processing",103:"Early Hints",200:"OK",201:"Created",202:"Accepted",203:"Non-Authoritative Information",204:"No Content",205:"Reset Content",206:"Partial Content",207:"Multi-Status",208:"Already Reported",226:"IM Used",300:"Multiple Choices",301:"Moved Permanently",302:"Found",303:"See Other",304:"Not Modified",305:"Use Proxy",307:"Temporary Redirect",308:"Permanent Redirect",400:"Bad Request",401:"Unauthorized",402:"Payment Required",403:"Forbidden",404:"Not Found",405:"Method Not Allowed",406:"Not Acceptable",407:"Proxy Authentication Required",408:"Request Timeout",409:"Conflict",410:"Gone",411:"Length Required",412:"Precondition Failed",413:"Payload Too Large",414:"URI Too Long",415:"Unsupported Media Type",416:"Range Not Satisfiable",417:"Expectation Failed",421:"Misdirected Request",422:"Unprocessable Entity",423:"Locked",424:"Failed Dependency",425:"Too Early",426:"Upgrade Required",428:"Precondition Required",429:"Too Many Requests",431:"Request Header Fields Too Large",451:"Unavailable For Legal Reasons",500:"Internal Server Error",501:"Not Implemented",502:"Bad Gateway",503:"Service Unavailable",504:"Gateway Timeout",505:"HTTP Version Not Supported",506:"Variant Also Negotiates",507:"Insufficient Storage",508:"Loop Detected",510:"Not Extended",511:"Network Authentication Required"},ie=function(){function r(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new te,n=arguments.length>1?arguments[1]:void 0;t(this,r),this.options=e,this.manualMap=n}return n(r,[{key:"createResponseHeaders",value:function(e,t,r){var n=e.headers?new Headers(e.headers):new Headers;return n.append("Cache-Control","no-store"),n.append("request-id",t),n.append("client-request-id",t),n.append("x-ms-ags-diagnostic",""),n.append("Date",r),n.append("Strict-Transport-Security",""),429===e.statusCode&&n.append("retry-after","3"),n}},{key:"createResponseBody",value:function(e,t,r){if(e.responseBody)return e.responseBody;var n;e.statusCode>=400?n={error:{code:ne[e.statusCode],message:e.statusMessage,innerError:{"request-id":t,date:r}}}:n={};return n}},{key:"createResponse",value:function(e,t){var r=t.request,n=U(),i=new Date,a=this.createResponseHeaders(e,n,i.toString()),o=this.createResponseBody(e,n,i.toString()),s={url:r,status:e.statusCode,statusText:e.statusMessage,headers:a};t.response=new Response("string"==typeof o?o:JSON.stringify(o),s)}},{key:"sendRequest",value:function(t,r){return s(this,void 0,void 0,o.mark((function n(){return o.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(this.setStatusCode(t,r.request,r.options.method),!(t.chaosStrategy===e.ChaosStrategy.MANUAL&&!this.nextMiddleware||Math.floor(100*Math.random())<t.chaosPercentage)){n.next=5;break}this.createResponse(t,r),n.next=8;break;case 5:if(!this.nextMiddleware){n.next=8;break}return n.next=8,this.nextMiddleware.execute(r);case 8:case"end":return n.stop()}}),n,this)})))}},{key:"getRandomStatusCode",value:function(e){var t=re[e];return t[Math.floor(Math.random()*t.length)]}},{key:"getRelativeURL",value:function(e){var t,r=/https?:\/\/graph\.microsoft\.com\/[^/]+(.+?)(\?|$)/;return null!==r.exec(e)&&(t=r.exec(e)[1]),t}},{key:"setStatusCode",value:function(t,r,n){var i=this;if(t.chaosStrategy===e.ChaosStrategy.MANUAL){if(void 0===t.statusCode){var a=this.getRelativeURL(r);void 0!==this.manualMap.get(a)?void 0!==this.manualMap.get(a).get(n)&&(t.statusCode=this.manualMap.get(a).get(n)):this.manualMap.forEach((function(e,r){new RegExp(r+"$").test(a)&&void 0!==i.manualMap.get(r).get(n)&&(t.statusCode=i.manualMap.get(r).get(n))}))}}else t.statusCode=this.getRandomStatusCode(n)}},{key:"getOptions",value:function(e){var t;return e.middlewareControl instanceof L&&(t=e.middlewareControl.getMiddlewareOptions(te)),void 0===t&&(t=Object.assign(new te,this.options)),t}},{key:"execute",value:function(e){return s(this,void 0,void 0,o.mark((function t(){var r;return o.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=this.getOptions(e),t.next=3,this.sendRequest(r,e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}}),t,this)})))}},{key:"setNext",value:function(e){this.nextMiddleware=e}}]),r}();e.ResponseType=void 0,(J=e.ResponseType||(e.ResponseType={})).ARRAYBUFFER="arraybuffer",J.BLOB="blob",J.DOCUMENT="document",J.JSON="json",J.RAW="raw",J.STREAM="stream",J.TEXT="text",function(e){e.TEXT_HTML="text/html",e.TEXT_XML="text/xml",e.APPLICATION_XML="application/xml",e.APPLICATION_XHTML="application/xhtml+xml"}(Z||(Z={})),function(e){e.TEXT_PLAIN="text/plain",e.APPLICATION_JSON="application/json"}(K||(K={})),function(e){e.DOCUMENT="^(text\\/(html|xml))|(application\\/(xml|xhtml\\+xml))$",e.IMAGE="^image\\/.+"}(ee||(ee={}));var ae=function(){function r(){t(this,r)}return n(r,null,[{key:"parseDocumentResponse",value:function(e,t){return"undefined"!=typeof DOMParser?new Promise((function(r,n){e.text().then((function(e){try{var i=(new DOMParser).parseFromString(e,t);r(i)}catch(e){n(e)}}))})):Promise.resolve(e.body)}},{key:"convertResponse",value:function(t,n){return s(this,void 0,void 0,o.mark((function i(){var a,s,u;return o.wrap((function(i){for(;;)switch(i.prev=i.next){case 0:if(204!==t.status){i.next=2;break}return i.abrupt("return",Promise.resolve());case 2:s=t.headers.get("Content-type"),i.t0=n,i.next=i.t0===e.ResponseType.ARRAYBUFFER?6:i.t0===e.ResponseType.BLOB?10:i.t0===e.ResponseType.DOCUMENT?14:i.t0===e.ResponseType.JSON?18:i.t0===e.ResponseType.STREAM?22:i.t0===e.ResponseType.TEXT?26:30;break;case 6:return i.next=8,t.arrayBuffer();case 8:return a=i.sent,i.abrupt("break",59);case 10:return i.next=12,t.blob();case 12:return a=i.sent,i.abrupt("break",59);case 14:return i.next=16,r.parseDocumentResponse(t,Z.TEXT_XML);case 16:return a=i.sent,i.abrupt("break",59);case 18:return i.next=20,t.json();case 20:return a=i.sent,i.abrupt("break",59);case 22:return i.next=24,Promise.resolve(t.body);case 24:return a=i.sent,i.abrupt("break",59);case 26:return i.next=28,t.text();case 28:return a=i.sent,i.abrupt("break",59);case 30:if(null===s){i.next=57;break}if(u=s.split(";")[0],!new RegExp(ee.DOCUMENT).test(u)){i.next=38;break}return i.next=35,r.parseDocumentResponse(t,u);case 35:a=i.sent,i.next=55;break;case 38:if(!new RegExp(ee.IMAGE).test(u)){i.next=42;break}a=t.blob(),i.next=55;break;case 42:if(u!==K.TEXT_PLAIN){i.next=48;break}return i.next=45,t.text();case 45:a=i.sent,i.next=55;break;case 48:if(u!==K.APPLICATION_JSON){i.next=54;break}return i.next=51,t.json();case 51:a=i.sent,i.next=55;break;case 54:a=Promise.resolve(t.body);case 55:i.next=58;break;case 57:a=Promise.resolve(t.body);case 58:return i.abrupt("break",59);case 59:return i.abrupt("return",a);case 60:case"end":return i.stop()}}),i)})))}},{key:"getResponse",value:function(t,n,i){return s(this,void 0,void 0,o.mark((function a(){var s;return o.wrap((function(a){for(;;)switch(a.prev=a.next){case 0:if(n!==e.ResponseType.RAW){a.next=4;break}return a.abrupt("return",Promise.resolve(t));case 4:return a.next=6,r.convertResponse(t,n);case 6:if(s=a.sent,!t.ok){a.next=15;break}if("function"!=typeof i){a.next=12;break}i(null,s),a.next=13;break;case 12:return a.abrupt("return",s);case 13:a.next=16;break;case 15:throw s;case 16:case"end":return a.stop()}}),a)})))}}]),r}(),oe=n((function e(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:-1,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:-1;t(this,e),this.minValue=r,this.maxValue=n})),se=function(){function e(r,n){t(this,e),this._location=n,this._responseBody=r}return n(e,[{key:"location",get:function(){return this._location},set:function(e){this._location=e}},{key:"responseBody",get:function(){return this._responseBody},set:function(e){this._responseBody=e}}],[{key:"CreateUploadResult",value:function(t,r){return new e(t,r.get("location"))}}]),e}(),ue=function(){function r(e,n,i){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};if(t(this,r),this.DEFAULT_FILE_SIZE=5242880,this.client=e,!n.sliceFile)throw new k("Please pass the FileUpload object, StreamUpload object or any custom implementation of the FileObject interface");this.file=n,this.file=n,a.rangeSize||(a.rangeSize=this.DEFAULT_FILE_SIZE),this.options=a,this.uploadSession=i,this.nextRange=new oe(0,this.options.rangeSize-1)}return n(r,[{key:"parseRange",value:function(e){var t=e[0];if(void 0===t||""===t)return new oe;var r=t.split("-"),n=parseInt(r[0],10),i=parseInt(r[1],10);return Number.isNaN(i)&&(i=this.file.size-1),new oe(n,i)}},{key:"updateTaskStatus",value:function(e){this.uploadSession.expiry=new Date(e.expirationDateTime),this.nextRange=this.parseRange(e.nextExpectedRanges)}},{key:"getNextRange",value:function(){if(-1===this.nextRange.minValue)return this.nextRange;var e=this.nextRange.minValue,t=e+this.options.rangeSize-1;return t>=this.file.size&&(t=this.file.size-1),new oe(e,t)}},{key:"sliceFile",value:function(e){if(console.warn("The LargeFileUploadTask.sliceFile() function has been deprecated and moved into the FileObject interface."),this.file.content instanceof ArrayBuffer||this.file.content instanceof Blob||this.file.content instanceof Uint8Array)return this.file.content.slice(e.minValue,e.maxValue+1);throw new k("The LargeFileUploadTask.sliceFile() function expects only Blob, ArrayBuffer or Uint8Array file content. Please note that the sliceFile() function is deprecated.")}},{key:"upload",value:function(){return s(this,void 0,void 0,o.mark((function e(){var t,r,n,i,a,s,u,c;return o.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=this.options&&this.options.uploadEventHandlers;case 1:if(this.uploadSession.isCancelled){e.next=26;break}if(-1!==(r=this.getNextRange()).maxValue){e.next=7;break}throw(n=new Error("Task with which you are trying to upload is already completed, Please check for your uploaded file")).name="Invalid Session",n;case 7:return e.next=9,this.file.sliceFile(r);case 9:return i=e.sent,e.next=12,this.uploadSliceGetRawResponse(i,r,this.file.size);case 12:if(a=e.sent){e.next=15;break}throw new k("Something went wrong! Large file upload slice response is null.");case 15:return e.next=17,ae.getResponse(a);case 17:if(s=e.sent,!(201===a.status||200===a.status&&s.id)){e.next=21;break}return u=se.CreateUploadResult(s,a.headers),e.abrupt("return",u);case 21:c={expirationDateTime:s.expirationDateTime||s.ExpirationDateTime,nextExpectedRanges:s.NextExpectedRanges||s.nextExpectedRanges},this.updateTaskStatus(c),t&&t.progress&&t.progress(r,t.extraCallbackParam),e.next=1;break;case 26:case"end":return e.stop()}}),e,this)})))}},{key:"uploadSlice",value:function(e,t,r){return s(this,void 0,void 0,o.mark((function n(){return o.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,this.client.api(this.uploadSession.url).headers({"Content-Length":"".concat(t.maxValue-t.minValue+1),"Content-Range":"bytes ".concat(t.minValue,"-").concat(t.maxValue,"/").concat(r),"Content-Type":"application/octet-stream"}).put(e);case 2:return n.abrupt("return",n.sent);case 3:case"end":return n.stop()}}),n,this)})))}},{key:"uploadSliceGetRawResponse",value:function(t,r,n){return s(this,void 0,void 0,o.mark((function i(){return o.wrap((function(i){for(;;)switch(i.prev=i.next){case 0:return i.next=2,this.client.api(this.uploadSession.url).headers({"Content-Length":"".concat(r.maxValue-r.minValue+1),"Content-Range":"bytes ".concat(r.minValue,"-").concat(r.maxValue,"/").concat(n),"Content-Type":"application/octet-stream"}).responseType(e.ResponseType.RAW).put(t);case 2:return i.abrupt("return",i.sent);case 3:case"end":return i.stop()}}),i,this)})))}},{key:"cancel",value:function(){return s(this,void 0,void 0,o.mark((function t(){var r;return o.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.client.api(this.uploadSession.url).responseType(e.ResponseType.RAW).delete();case 2:return 204===(r=t.sent).status&&(this.uploadSession.isCancelled=!0),t.abrupt("return",r);case 5:case"end":return t.stop()}}),t,this)})))}},{key:"getStatus",value:function(){return s(this,void 0,void 0,o.mark((function e(){var t;return o.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.client.api(this.uploadSession.url).get();case 2:return t=e.sent,this.updateTaskStatus(t),e.abrupt("return",t);case 5:case"end":return e.stop()}}),e,this)})))}},{key:"resume",value:function(){return s(this,void 0,void 0,o.mark((function e(){return o.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.getStatus();case 2:return e.next=4,this.upload();case 4:return e.abrupt("return",e.sent);case 5:case"end":return e.stop()}}),e,this)})))}},{key:"getUploadSession",value:function(){return this.uploadSession}}],[{key:"createUploadSession",value:function(e,t,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return s(this,void 0,void 0,o.mark((function i(){var a,s;return o.wrap((function(i){for(;;)switch(i.prev=i.next){case 0:return i.next=2,e.api(t).headers(n).post(r);case 2:return a=i.sent,s={url:a.uploadUrl,expiry:new Date(a.expirationDateTime),isCancelled:!1},i.abrupt("return",s);case 5:case"end":return i.stop()}}),i)})))}}]),r}();function ce(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=g(e)););return e}function le(){return(le="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,r){var n=ce(e,t);if(n){var i=Object.getOwnPropertyDescriptor(n,t);return i.get?i.get.call(arguments.length<3?e:r):i.value}}).apply(this,arguments)}var de=function(){function e(r,n,i){if(t(this,e),this.content=r,this.name=n,this.size=i,!r||!n||!i)throw new k("Please provide the upload content, name of the file and size of the file")}return n(e,[{key:"sliceFile",value:function(e){return this.content.slice(e.minValue,e.maxValue+1)}}]),e}(),fe=5242880,he=function(e){return e>327680&&(e=320*Math.floor(e/327680)*1024),e},pe=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:fe,t=62914560;return e>t&&(e=t),he(e)};function ve(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=g(e);if(t){var i=g(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return m(this,r)}}var ye=function(e){v(i,e);var r=ve(i);function i(e,n,a,o){return t(this,i),r.call(this,e,n,a,o)}return n(i,[{key:"commit",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"rename";return s(this,void 0,void 0,o.mark((function r(){var n;return o.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return n={name:this.file.name,"@microsoft.graph.conflictBehavior":t,"@microsoft.graph.sourceUrl":this.uploadSession.url},r.next=3,this.client.api(e).put(n);case 3:return r.abrupt("return",r.sent);case 4:case"end":return r.stop()}}),r,this)})))}}],[{key:"constructCreateSessionUrl",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i.DEFAULT_UPLOAD_PATH;return e=e.trim(),""===(t=t.trim())&&(t="/"),"/"!==t[0]&&(t="/".concat(t)),"/"!==t[t.length-1]&&(t="".concat(t,"/")),"/me/drive/root:".concat(t.split("/").map((function(e){return encodeURIComponent(e)})).join("/")).concat(encodeURIComponent(e),":/createUploadSession")}},{key:"getFileInfo",value:function(e,t){var r,n;if("undefined"!=typeof Blob&&e instanceof Blob)n=(r=new File([e],t)).size;else if("undefined"!=typeof File&&e instanceof File)n=(r=e).size;else if("undefined"!=typeof Uint8Array&&e instanceof Uint8Array){var i=e;n=i.byteLength,r=i.buffer.slice(i.byteOffset,i.byteOffset+i.byteLength)}return{content:r,size:n}}},{key:"create",value:function(e,t,r){return s(this,void 0,void 0,o.mark((function n(){var a,s,u;return o.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(e&&t&&r){n.next=2;break}throw new k("Please provide the Graph client instance, file object and OneDriveLargeFileUploadOptions value");case 2:return a=r.fileName,s=i.getFileInfo(t,a),u=new de(s.content,a,s.size),n.abrupt("return",this.createTaskWithFileObject(e,u,r));case 6:case"end":return n.stop()}}),n,this)})))}},{key:"createTaskWithFileObject",value:function(e,t,r){return s(this,void 0,void 0,o.mark((function n(){var a,s,u,c;return o.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(e&&t&&r){n.next=2;break}throw new k("Please provide the Graph client instance, FileObject interface implementation and OneDriveLargeFileUploadOptions value");case 2:return a=i.constructCreateSessionUrl(r.fileName,r.path),s={fileName:r.fileName,fileDescription:r.fileDescription,conflictBehavior:r.conflictBehavior},n.next=6,i.createUploadSession(e,a,s);case 6:return u=n.sent,c=pe(r.rangeSize),n.abrupt("return",new i(e,t,u,{rangeSize:c,uploadEventHandlers:r.uploadEventHandlers}));case 9:case"end":return n.stop()}}),n)})))}},{key:"createUploadSession",value:function(e,t,r){var n=this,a=Object.create(null,{createUploadSession:{get:function(){return le(g(i),"createUploadSession",n)}}});return s(this,void 0,void 0,o.mark((function n(){var i;return o.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return i={item:{"@microsoft.graph.conflictBehavior":(null==r?void 0:r.conflictBehavior)||"rename",name:null==r?void 0:r.fileName,description:null==r?void 0:r.fileDescription}},n.abrupt("return",a.createUploadSession.call(this,e,t,i));case 2:case"end":return n.stop()}}),n,this)})))}}]),i}(ue);ye.DEFAULT_UPLOAD_PATH="/";var me=function(){function e(r,n,i){if(t(this,e),this.content=r,this.name=n,this.size=i,!r||!n||!i)throw new k("Please provide the Readable Stream content, name of the file and size of the file")}return n(e,[{key:"sliceFile",value:function(e){return s(this,void 0,void 0,o.mark((function t(){var r,n,i,a,s;return o.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e.maxValue-e.minValue+1,n=[],!this.previousSlice){t.next=14;break}if(!(e.minValue<this.previousSlice.range.minValue)){t.next=5;break}throw new k("An error occurred while uploading the stream. Please restart the stream upload from the first byte of the file.");case 5:if(!(e.minValue<this.previousSlice.range.maxValue)){t.next=14;break}if(i=this.previousSlice.range.minValue,a=this.previousSlice.range.maxValue,e.minValue!==i||e.maxValue!==a){t.next=10;break}return t.abrupt("return",this.previousSlice.fileSlice);case 10:if(e.maxValue!==a){t.next=12;break}return t.abrupt("return",this.previousSlice.fileSlice.slice(e.minValue,e.maxValue+1));case 12:n.push(this.previousSlice.fileSlice.slice(e.minValue,a+1)),r=e.maxValue-a;case 14:if(!this.content||!this.content.readable){t.next=26;break}if(!(this.content.readableLength>=r)){t.next=19;break}n.push(this.content.read(r)),t.next=24;break;case 19:return t.t0=n,t.next=22,this.readNBytesFromStream(r);case 22:t.t1=t.sent,t.t0.push.call(t.t0,t.t1);case 24:t.next=27;break;case 26:throw new k("Stream is not readable.");case 27:return s=Buffer.concat(n),this.previousSlice={fileSlice:s,range:e},t.abrupt("return",s);case 30:case"end":return t.stop()}}),t,this)})))}},{key:"readNBytesFromStream",value:function(e){var t=this;return new Promise((function(r,n){var i=[],a=e,o=0;t.content.on("end",(function(){if(a>0)return n(new k("Stream ended before reading required range size"))})),t.content.on("readable",(function(){for(var s;o<e&&null!==(s=t.content.read(a));)o+=s.length,i.push(s),a>0&&(a=e-o);return o===e?r(Buffer.concat(i)):t.content&&t.content.readable?void 0:n(new k("Error encountered while reading the stream during the upload"))}))}))}}]),e}(),ge=function(){function e(r,n,i,a){t(this,e),this.client=r,this.collection=n.value,this.nextLink=n["@odata.nextLink"],this.deltaLink=n["@odata.deltaLink"],this.callback=i,this.complete=!1,this.requestOptions=a}return n(e,[{key:"iterationHelper",value:function(){if(void 0===this.collection)return!1;for(var e=!0;e&&0!==this.collection.length;){var t=this.collection.shift();e=this.callback(t)}return e}},{key:"fetchAndUpdateNextPageData",value:function(){return s(this,void 0,void 0,o.mark((function e(){var t,r;return o.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.client.api(this.nextLink),this.requestOptions&&(this.requestOptions.headers&&(t=t.headers(this.requestOptions.headers)),this.requestOptions.middlewareOptions&&(t=t.middlewareOptions(this.requestOptions.middlewareOptions)),this.requestOptions.options&&(t=t.options(this.requestOptions.options))),e.next=4,t.get();case 4:r=e.sent,this.collection=r.value,this.nextLink=r["@odata.nextLink"],this.deltaLink=r["@odata.deltaLink"];case 8:case"end":return e.stop()}}),e,this)})))}},{key:"getDeltaLink",value:function(){return this.deltaLink}},{key:"iterate",value:function(){return s(this,void 0,void 0,o.mark((function e(){var t;return o.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=this.iterationHelper();case 1:if(!t){e.next=11;break}if(void 0===this.nextLink){e.next=8;break}return e.next=5,this.fetchAndUpdateNextPageData();case 5:t=this.iterationHelper(),e.next=9;break;case 8:t=!1;case 9:e.next=1;break;case 11:void 0===this.nextLink&&0===this.collection.length&&(this.complete=!0);case 12:case"end":return e.stop()}}),e,this)})))}},{key:"resume",value:function(){return s(this,void 0,void 0,o.mark((function e(){return o.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.iterate());case 1:case"end":return e.stop()}}),e,this)})))}},{key:"isComplete",value:function(){return this.complete}}]),e}();function we(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function xe(e){return function(e){if(Array.isArray(e))return we(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return we(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?we(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var be=function(){function e(r){t(this,e),this.provider=r}return n(e,[{key:"getAccessToken",value:function(){return s(this,void 0,void 0,o.mark((function e(){var t=this;return o.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e,r){t.provider((function(n,i){return s(t,void 0,void 0,o.mark((function t(){var a;return o.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!i){t.next=4;break}e(i),t.next=9;break;case 4:return n||("Access token is undefined or empty.\t\t\t\t\t\tPlease provide a valid token.\t\t\t\t\t\tFor more help - https://github.com/microsoftgraph/msgraph-sdk-javascript/blob/dev/docs/CustomAuthenticationProvider.md",n=new k("Access token is undefined or empty.\t\t\t\t\t\tPlease provide a valid token.\t\t\t\t\t\tFor more help - https://github.com/microsoftgraph/msgraph-sdk-javascript/blob/dev/docs/CustomAuthenticationProvider.md")),t.next=7,k.setGraphClientError(n);case 7:a=t.sent,r(a);case 9:case"end":return t.stop()}}),t)})))}))})));case 1:case"end":return e.stop()}}),e)})))}}]),e}();function Ee(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=g(e);if(t){var i=g(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return m(this,r)}}var ke=function(e){v(i,e);var r=Ee(i);function i(){var e,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:-1,a=arguments.length>1?arguments[1]:void 0,o=arguments.length>2?arguments[2]:void 0;return t(this,i),e=r.call(this,a||o&&o.message),Object.setPrototypeOf(h(e),i.prototype),e.statusCode=n,e.code=null,e.requestId=null,e.date=new Date,e.body=null,e.stack=o?o.stack:e.stack,e}return n(i)}(b(Error)),Re=function(){function e(){t(this,e)}return n(e,null,[{key:"constructError",value:function(e,t){var r=new ke(t,"",e);return void 0!==e.name&&(r.code=e.name),r.body=e.toString(),r.date=new Date,r}},{key:"constructErrorFromResponse",value:function(e,t){var r=e.error,n=new ke(t,r.message);return n.code=r.code,void 0!==r.innerError&&(n.requestId=r.innerError["request-id"],n.date=new Date(r.innerError.date)),n.body=JSON.stringify(r),n}},{key:"getError",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:-1,n=arguments.length>2?arguments[2]:void 0;return s(this,void 0,void 0,o.mark((function i(){var a;return o.wrap((function(i){for(;;)switch(i.prev=i.next){case 0:if(t&&t.error?a=e.constructErrorFromResponse(t,r):t instanceof Error?a=e.constructError(t,r):(a=new ke(r)).body=t,"function"!=typeof n){i.next=5;break}n(a,null),i.next=6;break;case 5:return i.abrupt("return",a);case 6:case"end":return i.stop()}}),i)})))}}]),e}();function Oe(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(!e)return;if("string"==typeof e)return Ae(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ae(e,t)}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return o=e.done,e},e:function(e){s=!0,a=e},f:function(){try{o||null==r.return||r.return()}finally{if(s)throw a}}}}function Ae(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Te=function(){function r(e,n,i){var a=this;t(this,r),this.parsePath=function(e){if(-1!==e.indexOf("https://")){var t=(e=e.replace("https://","")).indexOf("/");-1!==t&&(a.urlComponents.host="https://"+e.substring(0,t),e=e.substring(t+1,e.length));var r=e.indexOf("/");-1!==r&&(a.urlComponents.version=e.substring(0,r),e=e.substring(r+1,e.length))}"/"===e.charAt(0)&&(e=e.substr(1));var n=e.indexOf("?");if(-1===n)a.urlComponents.path=e;else{a.urlComponents.path=e.substr(0,n);var i,o=Oe(e.substring(n+1,e.length).split("&"));try{for(o.s();!(i=o.n()).done;){var s=i.value;a.parseQueryParameter(s)}}catch(e){o.e(e)}finally{o.f()}}},this.httpClient=e,this.config=n,this.urlComponents={host:this.config.baseUrl,version:this.config.defaultVersion,oDataQueryParams:{},otherURLQueryParams:{},otherURLQueryOptions:[]},this._headers={},this._options={},this._middlewareOptions=[],this.parsePath(i)}return n(r,[{key:"addCsvQueryParameter",value:function(e,t,r){this.urlComponents.oDataQueryParams[e]=this.urlComponents.oDataQueryParams[e]?this.urlComponents.oDataQueryParams[e]+",":"";var n=[];r.length>1&&"string"==typeof t?n=Array.prototype.slice.call(r):"string"==typeof t?n.push(t):n=n.concat(t),this.urlComponents.oDataQueryParams[e]+=n.join(",")}},{key:"buildFullUrl",value:function(){var e,t,r=(e=[this.urlComponents.host,this.urlComponents.version,this.urlComponents.path],t=function(e){return e.replace(/^\/+/,"")},Array.prototype.slice.call(e).reduce((function(e,r){return[(n=e,n.replace(/\/+$/,"")),t(r)].join("/");var n}))+this.createQueryString());return this.config.debugLogging&&console.log(r),r}},{key:"createQueryString",value:function(){var e=this.urlComponents,t=[];if(0!==Object.keys(e.oDataQueryParams).length)for(var r in e.oDataQueryParams)Object.prototype.hasOwnProperty.call(e.oDataQueryParams,r)&&t.push(r+"="+e.oDataQueryParams[r]);if(0!==Object.keys(e.otherURLQueryParams).length)for(var n in e.otherURLQueryParams)Object.prototype.hasOwnProperty.call(e.otherURLQueryParams,n)&&t.push(n+"="+e.otherURLQueryParams[n]);if(0!==e.otherURLQueryOptions.length){var i,a=Oe(e.otherURLQueryOptions);try{for(a.s();!(i=a.n()).done;){var o=i.value;t.push(o)}}catch(e){a.e(e)}finally{a.f()}}return t.length>0?"?"+t.join("&"):""}},{key:"parseQueryParameter",value:function(e){if("string"==typeof e)if("?"===e.charAt(0)&&(e=e.substring(1)),-1!==e.indexOf("&")){var t,r=Oe(e.split("&"));try{for(r.s();!(t=r.n()).done;){var n=t.value;this.parseQueryParamenterString(n)}}catch(e){r.e(e)}finally{r.f()}}else this.parseQueryParamenterString(e);else if(e.constructor===Object)for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&this.setURLComponentsQueryParamater(i,e[i]);return this}},{key:"parseQueryParamenterString",value:function(e){if(this.isValidQueryKeyValuePair(e)){var t=e.indexOf("="),r=e.substring(0,t),n=e.substring(t+1);this.setURLComponentsQueryParamater(r,n)}else this.urlComponents.otherURLQueryOptions.push(e)}},{key:"setURLComponentsQueryParamater",value:function(e,t){if(-1!==R.indexOf(e)){var r=this.urlComponents.oDataQueryParams[e],n=r&&("$expand"===e||"$select"===e||"$orderby"===e);this.urlComponents.oDataQueryParams[e]=n?r+","+t:t}else this.urlComponents.otherURLQueryParams[e]=t}},{key:"isValidQueryKeyValuePair",value:function(e){var t=e.indexOf("=");return-1!==t&&!(-1!==e.indexOf("(")&&e.indexOf("(")<t)}},{key:"updateRequestOptions",value:function(e){var t=Object.assign({},e.headers);if(void 0!==this.config.fetchOptions){var r=Object.assign({},this.config.fetchOptions);Object.assign(e,r),void 0!==y(this.config.fetchOptions.headers)&&(e.headers=Object.assign({},this.config.fetchOptions.headers))}Object.assign(e,this._options),void 0!==e.headers&&Object.assign(t,e.headers),Object.assign(t,this._headers),e.headers=t}},{key:"send",value:function(e,t,r){var n;return s(this,void 0,void 0,o.mark((function i(){var a,s,u,c,l,d;return o.wrap((function(i){for(;;)switch(i.prev=i.next){case 0:return s=new L(this._middlewareOptions),this.updateRequestOptions(t),u=null===(n=this.config)||void 0===n?void 0:n.customHosts,i.prev=3,i.next=6,this.httpClient.sendRequest({request:e,options:t,middlewareControl:s,customHosts:u});case 6:return c=i.sent,a=c.response,i.next=10,ae.getResponse(a,this._responseType,r);case 10:return l=i.sent,i.abrupt("return",l);case 14:if(i.prev=14,i.t0=i.catch(3),!(i.t0 instanceof k)){i.next=18;break}throw i.t0;case 18:return a&&(d=a.status),i.next=21,Re.getError(i.t0,d,r);case 21:throw i.sent;case 23:case"end":return i.stop()}}),i,this,[[3,14]])})))}},{key:"setHeaderContentType",value:function(){if(this._headers){for(var e=0,t=Object.keys(this._headers);e<t.length;e++){if("content-type"===t[e].toLowerCase())return}this.header("Content-Type","application/json")}else this.header("Content-Type","application/json")}},{key:"header",value:function(e,t){return this._headers[e]=t,this}},{key:"headers",value:function(e){for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(this._headers[t]=e[t]);return this}},{key:"option",value:function(e,t){return this._options[e]=t,this}},{key:"options",value:function(e){for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(this._options[t]=e[t]);return this}},{key:"middlewareOptions",value:function(e){return this._middlewareOptions=e,this}},{key:"version",value:function(e){return this.urlComponents.version=e,this}},{key:"responseType",value:function(e){return this._responseType=e,this}},{key:"select",value:function(e){return this.addCsvQueryParameter("$select",e,arguments),this}},{key:"expand",value:function(e){return this.addCsvQueryParameter("$expand",e,arguments),this}},{key:"orderby",value:function(e){return this.addCsvQueryParameter("$orderby",e,arguments),this}},{key:"filter",value:function(e){return this.urlComponents.oDataQueryParams.$filter=e,this}},{key:"search",value:function(e){return this.urlComponents.oDataQueryParams.$search=e,this}},{key:"top",value:function(e){return this.urlComponents.oDataQueryParams.$top=e,this}},{key:"skip",value:function(e){return this.urlComponents.oDataQueryParams.$skip=e,this}},{key:"skipToken",value:function(e){return this.urlComponents.oDataQueryParams.$skipToken=e,this}},{key:"count",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return this.urlComponents.oDataQueryParams.$count=e.toString(),this}},{key:"query",value:function(e){return this.parseQueryParameter(e)}},{key:"get",value:function(e){return s(this,void 0,void 0,o.mark((function t(){var r,n,i;return o.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=this.buildFullUrl(),n={method:a.GET},t.next=4,this.send(r,n,e);case 4:return i=t.sent,t.abrupt("return",i);case 6:case"end":return t.stop()}}),t,this)})))}},{key:"post",value:function(e,t){return s(this,void 0,void 0,o.mark((function r(){var n,i;return o.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return n=this.buildFullUrl(),i={method:a.POST,body:O(e)},"FormData"===(e&&e.constructor&&e.constructor.name)?i.headers={}:(this.setHeaderContentType(),i.headers=this._headers),r.next=6,this.send(n,i,t);case 6:return r.abrupt("return",r.sent);case 7:case"end":return r.stop()}}),r,this)})))}},{key:"create",value:function(e,t){return s(this,void 0,void 0,o.mark((function r(){return o.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,this.post(e,t);case 2:return r.abrupt("return",r.sent);case 3:case"end":return r.stop()}}),r,this)})))}},{key:"put",value:function(e,t){return s(this,void 0,void 0,o.mark((function r(){var n,i;return o.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return n=this.buildFullUrl(),this.setHeaderContentType(),i={method:a.PUT,body:O(e)},r.next=5,this.send(n,i,t);case 5:return r.abrupt("return",r.sent);case 6:case"end":return r.stop()}}),r,this)})))}},{key:"patch",value:function(e,t){return s(this,void 0,void 0,o.mark((function r(){var n,i;return o.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return n=this.buildFullUrl(),this.setHeaderContentType(),i={method:a.PATCH,body:O(e)},r.next=5,this.send(n,i,t);case 5:return r.abrupt("return",r.sent);case 6:case"end":return r.stop()}}),r,this)})))}},{key:"update",value:function(e,t){return s(this,void 0,void 0,o.mark((function r(){return o.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,this.patch(e,t);case 2:return r.abrupt("return",r.sent);case 3:case"end":return r.stop()}}),r,this)})))}},{key:"delete",value:function(e){return s(this,void 0,void 0,o.mark((function t(){var r,n;return o.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=this.buildFullUrl(),n={method:a.DELETE},t.next=4,this.send(r,n,e);case 4:return t.abrupt("return",t.sent);case 5:case"end":return t.stop()}}),t,this)})))}},{key:"del",value:function(e){return s(this,void 0,void 0,o.mark((function t(){return o.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.delete(e);case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t,this)})))}},{key:"getStream",value:function(t){return s(this,void 0,void 0,o.mark((function r(){var n,i;return o.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return n=this.buildFullUrl(),i={method:a.GET},this.responseType(e.ResponseType.STREAM),r.next=5,this.send(n,i,t);case 5:return r.abrupt("return",r.sent);case 6:case"end":return r.stop()}}),r,this)})))}},{key:"putStream",value:function(e,t){return s(this,void 0,void 0,o.mark((function r(){var n,i;return o.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return n=this.buildFullUrl(),i={method:a.PUT,headers:{"Content-Type":"application/octet-stream"},body:e},r.next=4,this.send(n,i,t);case 4:return r.abrupt("return",r.sent);case 5:case"end":return r.stop()}}),r,this)})))}}]),r}(),Se=function(){function e(){for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];if(t(this,e),!n||!n.length){var a=new Error;throw a.name="InvalidMiddlewareChain",a.message="Please provide a default middleware chain or custom middleware chain",a}this.setMiddleware.apply(this,n)}return n(e,[{key:"setMiddleware",value:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];t.length>1?this.parseMiddleWareArray(t):this.middleware=t[0]}},{key:"parseMiddleWareArray",value:function(e){e.forEach((function(t,r){r<e.length-1&&t.setNext(e[r+1])})),this.middleware=e[0]}},{key:"sendRequest",value:function(e){return s(this,void 0,void 0,o.mark((function t(){var r;return o.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if("string"!=typeof e.request||void 0!==e.options){t.next=5;break}throw(r=new Error).name="InvalidRequestOptions",r.message="Unable to execute the middleware, Please provide valid options for a request",r;case 5:return t.next=7,this.middleware.execute(e);case 7:return t.abrupt("return",e);case 8:case"end":return t.stop()}}),t,this)})))}}]),e}(),Pe=function(){function e(){t(this,e)}return n(e,null,[{key:"createWithAuthenticationProvider",value:function(t){var r=new B(t),n=new X(new V),i=new z,a=new Q;if(r.setNext(n),"object"===("undefined"==typeof process?"undefined":y(process))&&"function"==typeof require){var o=new $(new G);n.setNext(o),o.setNext(i)}else n.setNext(i);return i.setNext(a),e.createWithMiddleware(r)}},{key:"createWithMiddleware",value:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return x(Se,t)}}]),e}(),Ce=function(){function e(r){for(var n in t(this,e),this.config={baseUrl:"https://graph.microsoft.com/",debugLogging:!1,defaultVersion:"v1.0"},function(){if("undefined"==typeof Promise&&"undefined"==typeof fetch){var e=new Error("Library cannot function without Promise and fetch. So, please provide polyfill for them.");throw e.name="PolyFillNotAvailable",e}if("undefined"==typeof Promise){var t=new Error("Library cannot function without Promise. So, please provide polyfill for it.");throw t.name="PolyFillNotAvailable",t}if("undefined"==typeof fetch){var r=new Error("Library cannot function without fetch. So, please provide polyfill for it.");throw r.name="PolyFillNotAvailable",r}}(),r)Object.prototype.hasOwnProperty.call(r,n)&&(this.config[n]=r[n]);var i;if(void 0!==r.authProvider&&void 0!==r.middleware){var a=new Error;throw a.name="AmbiguityInInitialization",a.message="Unable to Create Client, Please provide either authentication provider for default middleware chain or custom middleware chain not both",a}if(void 0!==r.authProvider)i=Pe.createWithAuthenticationProvider(r.authProvider);else{if(void 0===r.middleware){var o=new Error;throw o.name="InvalidMiddlewareChain",o.message="Unable to Create Client, Please provide either authentication provider for default middleware chain or custom middleware chain",o}i=x(Se,xe([].concat(r.middleware)))}this.httpClient=i}return n(e,[{key:"api",value:function(e){return new Te(this.httpClient,this.config,e)}}],[{key:"init",value:function(t){var r={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(r[n]="authProvider"===n?new be(t[n]):t[n]);return e.initWithMiddleware(r)}},{key:"initWithMiddleware",value:function(t){return new e(t)}}]),e}();return e.AuthenticationHandler=B,e.AuthenticationHandlerOptions=H,e.BatchRequestContent=l,e.BatchResponseContent=d,e.ChaosHandler=ie,e.ChaosHandlerOptions=te,e.Client=Ce,e.CustomAuthenticationProvider=be,e.FileUpload=de,e.GraphClientError=k,e.GraphError=ke,e.GraphRequest=Te,e.HTTPMessageHandler=Q,e.LargeFileUploadTask=ue,e.MiddlewareFactory=W,e.OneDriveLargeFileUploadTask=ye,e.PageIterator=ge,e.Range=oe,e.RedirectHandler=$,e.RedirectHandlerOptions=G,e.RetryHandler=X,e.RetryHandlerOptions=V,e.StreamUpload=me,e.TelemetryHandler=z,e.TelemetryHandlerOptions=j,e.UploadResult=se,e.getValidRangeSize=pe,Object.defineProperty(e,"__esModule",{value:!0}),e}({});
