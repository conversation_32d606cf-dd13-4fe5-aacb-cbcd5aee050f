{"version": 3, "file": "Client.js", "sourceRoot": "", "sources": ["../../src/Client.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;AAEH;;GAEG;AAEH,yCAAgE;AAChE,+EAA8E;AAC9E,+CAA8C;AAC9C,2CAA0C;AAC1C,yDAAwD;AAGxD,6DAA4D;AAE5D;IA6CC;;;;;OAKG;IACH,gBAAoB,aAA4B;QAlDhD;;;WAGG;QACK,WAAM,GAAkB;YAC/B,OAAO,EAAE,0BAAc;YACvB,YAAY,EAAE,KAAK;YACnB,cAAc,EAAE,6BAAiB;SACjC,CAAC;QA2CD,IAAA,yCAAmB,GAAE,CAAC;QACtB,KAAK,IAAM,GAAG,IAAI,aAAa,EAAE;YAChC,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,CAAC,EAAE;gBAC7D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;aACtC;SACD;QACD,IAAI,UAAsB,CAAC;QAC3B,IAAI,aAAa,CAAC,YAAY,KAAK,SAAS,IAAI,aAAa,CAAC,UAAU,KAAK,SAAS,EAAE;YACvF,IAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;YAC1B,KAAK,CAAC,IAAI,GAAG,2BAA2B,CAAC;YACzC,KAAK,CAAC,OAAO,GAAG,yIAAyI,CAAC;YAC1J,MAAM,KAAK,CAAC;SACZ;aAAM,IAAI,aAAa,CAAC,YAAY,KAAK,SAAS,EAAE;YACpD,UAAU,GAAG,qCAAiB,CAAC,gCAAgC,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;SAC5F;aAAM,IAAI,aAAa,CAAC,UAAU,KAAK,SAAS,EAAE;YAClD,UAAU,QAAO,uBAAU,YAAV,uBAAU,uCAAI,EAAE,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,YAAC,CAAC;SACpE;aAAM;YACN,IAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;YAC1B,KAAK,CAAC,IAAI,GAAG,wBAAwB,CAAC;YACtC,KAAK,CAAC,OAAO,GAAG,gIAAgI,CAAC;YACjJ,MAAM,KAAK,CAAC;SACZ;QACD,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC9B,CAAC;IA1DD;;;;;;OAMG;IACW,WAAI,GAAlB,UAAmB,OAAgB;QAClC,IAAM,aAAa,GAAkB,EAAE,CAAC;QACxC,KAAK,IAAM,CAAC,IAAI,OAAO,EAAE;YACxB,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE;gBACrD,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,cAAc,CAAC,CAAC,CAAC,IAAI,2DAA4B,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;aACpG;SACD;QACD,OAAO,MAAM,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;IACjD,CAAC;IAED;;;;;;OAMG;IACW,yBAAkB,GAAhC,UAAiC,aAA4B;QAC5D,OAAO,IAAI,MAAM,CAAC,aAAa,CAAC,CAAC;IAClC,CAAC;IAkCD;;;;;OAKG;IACI,oBAAG,GAAV,UAAW,IAAY;QACtB,OAAO,IAAI,2BAAY,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAC7D,CAAC;IACF,aAAC;AAAD,CAAC,AAtFD,IAsFC;AAtFY,wBAAM"}