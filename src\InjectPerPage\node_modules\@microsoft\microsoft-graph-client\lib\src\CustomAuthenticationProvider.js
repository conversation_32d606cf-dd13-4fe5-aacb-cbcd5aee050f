"use strict";
/**
 * -------------------------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation.  All Rights Reserved.  Licensed under the MIT License.
 * See License in the project root for license information.
 * -------------------------------------------------------------------------------------------
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomAuthenticationProvider = void 0;
var tslib_1 = require("tslib");
/**
 * @module CustomAuthenticationProvider
 */
var GraphClientError_1 = require("./GraphClientError");
/**
 * @class
 * Class representing CustomAuthenticationProvider
 * @extends AuthenticationProvider
 */
var CustomAuthenticationProvider = /** @class */ (function () {
    /**
     * @public
     * @constructor
     * Creates an instance of CustomAuthenticationProvider
     * @param {AuthProviderCallback} provider - An authProvider function
     * @returns An instance of CustomAuthenticationProvider
     */
    function CustomAuthenticationProvider(provider) {
        this.provider = provider;
    }
    /**
     * @public
     * @async
     * To get the access token
     * @returns The promise that resolves to an access token
     */
    CustomAuthenticationProvider.prototype.getAccessToken = function () {
        return (0, tslib_1.__awaiter)(this, void 0, void 0, function () {
            var _this = this;
            return (0, tslib_1.__generator)(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve, reject) {
                        _this.provider(function (error, accessToken) { return (0, tslib_1.__awaiter)(_this, void 0, void 0, function () {
                            var invalidTokenMessage, err;
                            return (0, tslib_1.__generator)(this, function (_a) {
                                switch (_a.label) {
                                    case 0:
                                        if (!accessToken) return [3 /*break*/, 1];
                                        resolve(accessToken);
                                        return [3 /*break*/, 3];
                                    case 1:
                                        if (!error) {
                                            invalidTokenMessage = "Access token is undefined or empty.\
						Please provide a valid token.\
						For more help - https://github.com/microsoftgraph/msgraph-sdk-javascript/blob/dev/docs/CustomAuthenticationProvider.md";
                                            error = new GraphClientError_1.GraphClientError(invalidTokenMessage);
                                        }
                                        return [4 /*yield*/, GraphClientError_1.GraphClientError.setGraphClientError(error)];
                                    case 2:
                                        err = _a.sent();
                                        reject(err);
                                        _a.label = 3;
                                    case 3: return [2 /*return*/];
                                }
                            });
                        }); });
                    })];
            });
        });
    };
    return CustomAuthenticationProvider;
}());
exports.CustomAuthenticationProvider = CustomAuthenticationProvider;
//# sourceMappingURL=CustomAuthenticationProvider.js.map