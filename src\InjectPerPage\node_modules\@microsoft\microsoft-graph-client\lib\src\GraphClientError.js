"use strict";
/**
 * -------------------------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation.  All Rights Reserved.  Licensed under the MIT License.
 * See License in the project root for license information.
 * -------------------------------------------------------------------------------------------
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.GraphClientError = void 0;
var tslib_1 = require("tslib");
/**
 * @module GraphClientError
 */
/**
 * @class
 * Create GraphClientError object to handle client-side errors
 * encountered within the JavaScript Client SDK.
 * Whereas GraphError Class should be used to handle errors in the response from the Graph API.
 */
var GraphClientError = /** @class */ (function (_super) {
    (0, tslib_1.__extends)(GraphClientError, _super);
    /**
     * @public
     * @constructor
     * Creates an instance of GraphClientError
     * @param {string} message? - Error message
     * @returns An instance of GraphClientError
     */
    function GraphClientError(message) {
        var _this = _super.call(this, message) || this;
        Object.setPrototypeOf(_this, GraphClientError.prototype);
        return _this;
    }
    /**
     * @public
     * @static
     * @async
     * To set the GraphClientError object
     * @param {any} error - The error returned encountered by the Graph JavaScript Client SDK while processing request
     * @returns GraphClientError object set to the error passed
     */
    GraphClientError.setGraphClientError = function (error) {
        var graphClientError;
        if (error instanceof Error) {
            graphClientError = error;
        }
        else {
            graphClientError = new GraphClientError();
            graphClientError.customError = error;
        }
        return graphClientError;
    };
    return GraphClientError;
}(Error));
exports.GraphClientError = GraphClientError;
//# sourceMappingURL=GraphClientError.js.map