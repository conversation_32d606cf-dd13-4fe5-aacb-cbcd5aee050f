"use strict";
/**
 * -------------------------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation.  All Rights Reserved.  Licensed under the MIT License.
 * See License in the project root for license information.
 * -------------------------------------------------------------------------------------------
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.GraphError = void 0;
var tslib_1 = require("tslib");
/**
 * @module GraphError
 */
/**
 * @class
 * Class for GraphError
 * @NOTE: This is NOT what is returned from the Graph
 * GraphError is created from parsing JSON errors returned from the graph
 * Some fields are renamed ie, "request-id" => requestId so you can use dot notation
 */
var GraphError = /** @class */ (function (_super) {
    (0, tslib_1.__extends)(GraphError, _super);
    /**
     * @public
     * @constructor
     * Creates an instance of GraphError
     * @param {number} [statusCode = -1] - The status code of the error
     * @param {string} [message] - The message of the error
     * @param {Error} [baseError] - The base error
     * @returns An instance of GraphError
     */
    function GraphError(statusCode, message, baseError) {
        if (statusCode === void 0) { statusCode = -1; }
        var _this = _super.call(this, message || (baseError && baseError.message)) || this;
        // https://github.com/Microsoft/TypeScript/wiki/Breaking-Changes#extending-built-ins-like-error-array-and-map-may-no-longer-work
        Object.setPrototypeOf(_this, GraphError.prototype);
        _this.statusCode = statusCode;
        _this.code = null;
        _this.requestId = null;
        _this.date = new Date();
        _this.body = null;
        _this.stack = baseError ? baseError.stack : _this.stack;
        return _this;
    }
    return GraphError;
}(Error));
exports.GraphError = GraphError;
//# sourceMappingURL=GraphError.js.map