{"version": 3, "file": "GraphErrorHandler.js", "sourceRoot": "", "sources": ["../../src/GraphErrorHandler.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;AAEH;;GAEG;AAEH,2CAA0C;AAiB1C;;;GAGG;AAEH;IAAA;IAiFA,CAAC;IAhFA;;;;;;;OAOG;IACY,gCAAc,GAA7B,UAA8B,KAAY,EAAE,UAAmB;QAC9D,IAAM,MAAM,GAAG,IAAI,uBAAU,CAAC,UAAU,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;QACrD,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,EAAE;YAC7B,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;SACzB;QACD,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;QAC/B,MAAM,CAAC,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;QACzB,OAAO,MAAM,CAAC;IACf,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACY,4CAA0B,GAAzC,UAA0C,UAAiC,EAAE,UAAkB;QAC9F,IAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC;QAC/B,IAAM,MAAM,GAAG,IAAI,uBAAU,CAAC,UAAU,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QACzD,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QACzB,IAAI,KAAK,CAAC,UAAU,KAAK,SAAS,EAAE;YACnC,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;YAClD,MAAM,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;SAC9C;QAED,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAEpC,OAAO,MAAM,CAAC;IACf,CAAC;IAED;;;;;;;;;;OAUG;IACiB,0BAAQ,GAA5B,UAA6B,KAAiB,EAAE,UAAe,EAAE,QAA+B;QAAnE,sBAAA,EAAA,YAAiB;QAAE,2BAAA,EAAA,cAAc,CAAC;;;;gBAE9D,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;oBACzB,MAAM,GAAG,iBAAiB,CAAC,0BAA0B,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;iBACzE;qBAAM,IAAI,KAAK,YAAY,KAAK,EAAE;oBAClC,MAAM,GAAG,iBAAiB,CAAC,cAAc,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;iBAC7D;qBAAM;oBACN,MAAM,GAAG,IAAI,uBAAU,CAAC,UAAU,CAAC,CAAC;oBACpC,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,4FAA4F;iBACjH;gBACD,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;oBACnC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;iBACvB;qBAAM;oBACN,sBAAO,MAAM,EAAC;iBACd;;;;KACD;IACF,wBAAC;AAAD,CAAC,AAjFD,IAiFC;AAjFY,8CAAiB"}