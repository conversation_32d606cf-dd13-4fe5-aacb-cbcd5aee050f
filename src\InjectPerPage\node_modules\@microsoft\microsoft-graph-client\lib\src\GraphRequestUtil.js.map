{"version": 3, "file": "GraphRequestUtil.js", "sourceRoot": "", "sources": ["../../src/GraphRequestUtil.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;AAEH;;GAEG;AACH,yCAAyC;AACzC,uDAAsD;AACtD;;GAEG;AACU,QAAA,eAAe,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;AAEtH;;;;GAIG;AACI,IAAM,OAAO,GAAG,UAAC,WAAqB;IAC5C,IAAM,eAAe,GAAG,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAArB,CAAqB,CAAC;IACrD,IAAM,cAAc,GAAG,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAArB,CAAqB,CAAC;IACpD,IAAM,MAAM,GAAG,UAAC,GAAG,EAAE,GAAG,IAAK,OAAA,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAArD,CAAqD,CAAC;IACnF,IAAM,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACtD,OAAO,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC7B,CAAC,CAAC;AANW,QAAA,OAAO,WAMlB;AAEF;;;;;;;;;;;GAWG;AAEI,IAAM,gBAAgB,GAAG,UAAC,OAAY;IAC5C,IAAM,SAAS,GAAW,OAAO,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC;IACrF,IAAI,SAAS,KAAK,QAAQ,IAAI,SAAS,KAAK,MAAM,IAAI,SAAS,KAAK,MAAM,IAAI,SAAS,KAAK,UAAU,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QACtI,OAAO,OAAO,CAAC;KACf;IACD,IAAI,SAAS,KAAK,aAAa,EAAE;QAChC,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KAC/B;SAAM,IAAI,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,YAAY,IAAI,SAAS,KAAK,YAAY,IAAI,SAAS,KAAK,YAAY,IAAI,SAAS,KAAK,aAAa,IAAI,SAAS,KAAK,aAAa,IAAI,SAAS,KAAK,mBAAmB,IAAI,SAAS,KAAK,cAAc,IAAI,SAAS,KAAK,cAAc,IAAI,SAAS,KAAK,UAAU,EAAE;QAC9T,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;KACtC;SAAM;QACN,IAAI;YACH,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;SAClC;QAAC,OAAO,KAAK,EAAE;YACf,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;SACnD;KACD;IACD,OAAO,OAAO,CAAC;AAChB,CAAC,CAAC;AAjBW,QAAA,gBAAgB,oBAiB3B;AAEF;;;;GAIG;AACI,IAAM,UAAU,GAAG,UAAC,GAAW;IACrC,OAAO,eAAe,CAAC,GAAG,CAAC,CAAC;AAC7B,CAAC,CAAC;AAFW,QAAA,UAAU,cAErB;AAEF;;;;;GAKG;AACI,IAAM,YAAY,GAAG,UAAC,GAAW,EAAE,WAAwB;IACjE,WAAW,CAAC,OAAO,CAAC,UAAC,IAAI,IAAK,OAAA,iBAAiB,CAAC,IAAI,CAAC,EAAvB,CAAuB,CAAC,CAAC;IACvD,OAAO,eAAe,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;AAC1C,CAAC,CAAC;AAHW,QAAA,YAAY,gBAGvB;AAEF;;;;;GAKG;AACH,IAAM,eAAe,GAAG,UAAC,GAAW,EAAE,YAAsC;IAAtC,6BAAA,EAAA,eAA4B,sBAAU;IAC3E,gGAAgG;IAChG,8DAA8D;IAC9D,GAAG,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;IAExB,IAAI,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;QACnC,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;QAElC,2BAA2B;QAC3B,IAAM,gBAAgB,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAM,eAAe,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACzC,IAAI,QAAQ,GAAG,EAAE,CAAC;QAClB,IAAI,eAAe,KAAK,CAAC,CAAC,EAAE;YAC3B,IAAI,gBAAgB,KAAK,CAAC,CAAC,IAAI,gBAAgB,GAAG,eAAe,EAAE;gBAClE,QAAQ,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC;gBAC9C,OAAO,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;aAClC;YACD,qBAAqB;YACrB,QAAQ,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC;YAC7C,OAAO,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;SAClC;KACD;IAED,OAAO,KAAK,CAAC;AACd,CAAC,CAAC;AAEF;;;GAGG;AACH,IAAM,iBAAiB,GAAG,UAAC,IAAY;IACtC,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;QAC7B,MAAM,IAAI,mCAAgB,CAAC,gIAAgI,CAAC,CAAC;KAC7J;AACF,CAAC,CAAC"}