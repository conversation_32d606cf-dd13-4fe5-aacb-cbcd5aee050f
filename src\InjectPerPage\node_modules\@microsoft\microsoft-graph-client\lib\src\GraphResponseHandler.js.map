{"version": 3, "file": "GraphResponseHandler.js", "sourceRoot": "", "sources": ["../../src/GraphResponseHandler.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;AAQH,+CAA8C;AAE9C;;;;;;;GAOG;AACH,IAAY,YAKX;AALD,WAAY,YAAY;IACvB,uCAAuB,CAAA;IACvB,qCAAqB,CAAA;IACrB,mDAAmC,CAAA;IACnC,2DAA2C,CAAA;AAC5C,CAAC,EALW,YAAY,GAAZ,oBAAY,KAAZ,oBAAY,QAKvB;AAED;;;;;GAKG;AAEH,IAAK,WAGJ;AAHD,WAAK,WAAW;IACf,wCAAyB,CAAA;IACzB,oDAAqC,CAAA;AACtC,CAAC,EAHI,WAAW,KAAX,WAAW,QAGf;AAED;;;;;GAKG;AACH,IAAK,mBAGJ;AAHD,WAAK,mBAAmB;IACvB,2FAAoE,CAAA;IACpE,4CAAqB,CAAA;AACtB,CAAC,EAHI,mBAAmB,KAAnB,mBAAmB,QAGvB;AAED;;;GAGG;AAEH;IAAA;IA2HA,CAAC;IA1HA;;;;;;;OAOG;IACY,0CAAqB,GAApC,UAAqC,WAAqB,EAAE,IAAkB;QAC7E,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE;YACrC,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;gBAClC,WAAW,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,UAAC,SAAS;oBACjC,IAAI;wBACH,IAAM,MAAM,GAAG,IAAI,SAAS,EAAE,CAAC;wBAC/B,IAAM,MAAM,GAAG,MAAM,CAAC,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;wBACvD,OAAO,CAAC,MAAM,CAAC,CAAC;qBAChB;oBAAC,OAAO,KAAK,EAAE;wBACf,MAAM,CAAC,KAAK,CAAC,CAAC;qBACd;gBACF,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC;SACH;aAAM;YACN,OAAO,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SACzC;IACF,CAAC;IAED;;;;;;;;OAQG;IACkB,oCAAe,GAApC,UAAqC,WAAqB,EAAE,YAA2B;;;;;;wBACtF,IAAI,WAAW,CAAC,MAAM,KAAK,GAAG,EAAE;4BAC/B,aAAa;4BACb,sBAAO,OAAO,CAAC,OAAO,EAAE,EAAC;yBACzB;wBAEK,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;wBACpD,KAAA,YAAY,CAAA;;iCACd,2BAAY,CAAC,WAAW,CAAC,CAAzB,wBAAwB;iCAGxB,2BAAY,CAAC,IAAI,CAAC,CAAlB,wBAAiB;iCAGjB,2BAAY,CAAC,QAAQ,CAAC,CAAtB,wBAAqB;iCAGrB,2BAAY,CAAC,IAAI,CAAC,CAAlB,wBAAiB;iCAGjB,2BAAY,CAAC,MAAM,CAAC,CAApB,wBAAmB;iCAGnB,2BAAY,CAAC,IAAI,CAAC,CAAlB,yBAAiB;;;4BAdL,qBAAM,WAAW,CAAC,WAAW,EAAE,EAAA;;wBAA/C,aAAa,GAAG,SAA+B,CAAC;wBAChD,yBAAM;4BAEU,qBAAM,WAAW,CAAC,IAAI,EAAE,EAAA;;wBAAxC,aAAa,GAAG,SAAwB,CAAC;wBACzC,yBAAM;4BAEU,qBAAM,oBAAoB,CAAC,qBAAqB,CAAC,WAAW,EAAE,YAAY,CAAC,QAAQ,CAAC,EAAA;;wBAApG,aAAa,GAAG,SAAoF,CAAC;wBACrG,yBAAM;4BAEU,qBAAM,WAAW,CAAC,IAAI,EAAE,EAAA;;wBAAxC,aAAa,GAAG,SAAwB,CAAC;wBACzC,yBAAM;4BAEU,qBAAM,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAA;;wBAAvD,aAAa,GAAG,SAAuC,CAAC;wBACxD,yBAAM;6BAEU,qBAAM,WAAW,CAAC,IAAI,EAAE,EAAA;;wBAAxC,aAAa,GAAG,SAAwB,CAAC;wBACzC,yBAAM;;6BAEF,CAAA,WAAW,KAAK,IAAI,CAAA,EAApB,yBAAoB;wBACjB,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;6BACvC,IAAI,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAvD,yBAAuD;wBAC1C,qBAAM,oBAAoB,CAAC,qBAAqB,CAAC,WAAW,EAAE,QAAwB,CAAC,EAAA;;wBAAvG,aAAa,GAAG,SAAuF,CAAC;;;6BAC9F,IAAI,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAApD,yBAAoD;wBAC9D,aAAa,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC;;;6BACzB,CAAA,QAAQ,KAAK,WAAW,CAAC,UAAU,CAAA,EAAnC,yBAAmC;wBAC7B,qBAAM,WAAW,CAAC,IAAI,EAAE,EAAA;;wBAAxC,aAAa,GAAG,SAAwB,CAAC;;;6BAC/B,CAAA,QAAQ,KAAK,WAAW,CAAC,gBAAgB,CAAA,EAAzC,yBAAyC;wBACnC,qBAAM,WAAW,CAAC,IAAI,EAAE,EAAA;;wBAAxC,aAAa,GAAG,SAAwB,CAAC;;;wBAEzC,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;;;;wBAGnD;;;;;;;;;;2BAUG;wBACH,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;;6BAEnD,yBAAM;6BAER,sBAAO,aAAa,EAAC;;;;KACrB;IAED;;;;;;;;;OASG;IACiB,gCAAW,GAA/B,UAAgC,WAAqB,EAAE,YAA2B,EAAE,QAA+B;;;;;;6BAC9G,CAAA,YAAY,KAAK,2BAAY,CAAC,GAAG,CAAA,EAAjC,wBAAiC;wBACpC,sBAAO,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,EAAC;4BAEnB,qBAAM,oBAAoB,CAAC,eAAe,CAAC,WAAW,EAAE,YAAY,CAAC,EAAA;;wBAAhF,QAAQ,GAAG,SAAqE;wBACtF,IAAI,WAAW,CAAC,EAAE,EAAE;4BACnB,kBAAkB;4BAClB,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;gCACnC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;6BACzB;iCAAM;gCACN,sBAAO,QAAQ,EAAC;6BAChB;yBACD;6BAAM;4BACN,kBAAkB;4BAClB,MAAM,QAAQ,CAAC;yBACf;;;;;;KAEF;IACF,2BAAC;AAAD,CAAC,AA3HD,IA2HC;AA3HY,oDAAoB"}