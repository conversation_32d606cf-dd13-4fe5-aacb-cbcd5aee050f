"use strict";
/**
 * -------------------------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation.  All Rights Reserved.  Licensed under the MIT License.
 * See License in the project root for license information.
 * -------------------------------------------------------------------------------------------
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.HTTPClient = void 0;
var tslib_1 = require("tslib");
/**
 * @class
 * Class representing HTTPClient
 */
var HTTPClient = /** @class */ (function () {
    /**
     * @public
     * @constructor
     * Creates an instance of a HTTPClient
     * @param {...Middleware} middleware - The first middleware of the middleware chain or a sequence of all the Middleware handlers
     */
    function HTTPClient() {
        var middleware = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            middleware[_i] = arguments[_i];
        }
        if (!middleware || !middleware.length) {
            var error = new Error();
            error.name = "InvalidMiddlewareChain";
            error.message = "Please provide a default middleware chain or custom middleware chain";
            throw error;
        }
        this.setMiddleware.apply(this, middleware);
    }
    /**
     * @private
     * Processes the middleware parameter passed to set this.middleware property
     * The calling function should validate if middleware is not undefined or not empty.
     * @param {...Middleware} middleware - The middleware passed
     * @returns Nothing
     */
    HTTPClient.prototype.setMiddleware = function () {
        var middleware = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            middleware[_i] = arguments[_i];
        }
        if (middleware.length > 1) {
            this.parseMiddleWareArray(middleware);
        }
        else {
            this.middleware = middleware[0];
        }
    };
    /**
     * @private
     * Processes the middleware array to construct the chain
     * and sets this.middleware property to the first middleware handler of the array
     * The calling function should validate if middleware is not undefined or not empty
     * @param {Middleware[]} middlewareArray - The array of middleware handlers
     * @returns Nothing
     */
    HTTPClient.prototype.parseMiddleWareArray = function (middlewareArray) {
        middlewareArray.forEach(function (element, index) {
            if (index < middlewareArray.length - 1) {
                element.setNext(middlewareArray[index + 1]);
            }
        });
        this.middleware = middlewareArray[0];
    };
    /**
     * @public
     * @async
     * To send the request through the middleware chain
     * @param {Context} context - The context of a request
     * @returns A promise that resolves to the Context
     */
    HTTPClient.prototype.sendRequest = function (context) {
        return (0, tslib_1.__awaiter)(this, void 0, void 0, function () {
            var error;
            return (0, tslib_1.__generator)(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (typeof context.request === "string" && context.options === undefined) {
                            error = new Error();
                            error.name = "InvalidRequestOptions";
                            error.message = "Unable to execute the middleware, Please provide valid options for a request";
                            throw error;
                        }
                        return [4 /*yield*/, this.middleware.execute(context)];
                    case 1:
                        _a.sent();
                        return [2 /*return*/, context];
                }
            });
        });
    };
    return HTTPClient;
}());
exports.HTTPClient = HTTPClient;
//# sourceMappingURL=HTTPClient.js.map