{"version": 3, "file": "HTTPClient.js", "sourceRoot": "", "sources": ["../../src/HTTPClient.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;AASH;;;GAGG;AACH;IAOC;;;;;OAKG;IACH;QAAmB,oBAA2B;aAA3B,UAA2B,EAA3B,qBAA2B,EAA3B,IAA2B;YAA3B,+BAA2B;;QAC7C,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;YACtC,IAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;YAC1B,KAAK,CAAC,IAAI,GAAG,wBAAwB,CAAC;YACtC,KAAK,CAAC,OAAO,GAAG,sEAAsE,CAAC;YACvF,MAAM,KAAK,CAAC;SACZ;QACD,IAAI,CAAC,aAAa,OAAlB,IAAI,EAAkB,UAAU,EAAE;IACnC,CAAC;IAED;;;;;;OAMG;IACK,kCAAa,GAArB;QAAsB,oBAA2B;aAA3B,UAA2B,EAA3B,qBAA2B,EAA3B,IAA2B;YAA3B,+BAA2B;;QAChD,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;SACtC;aAAM;YACN,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;SAChC;IACF,CAAC;IAED;;;;;;;OAOG;IACK,yCAAoB,GAA5B,UAA6B,eAA6B;QACzD,eAAe,CAAC,OAAO,CAAC,UAAC,OAAO,EAAE,KAAK;YACtC,IAAI,KAAK,GAAG,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;gBACvC,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;aAC5C;QACF,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,UAAU,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC;IAED;;;;;;OAMG;IACU,gCAAW,GAAxB,UAAyB,OAAgB;;;;;;wBACxC,IAAI,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE;4BACnE,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;4BAC1B,KAAK,CAAC,IAAI,GAAG,uBAAuB,CAAC;4BACrC,KAAK,CAAC,OAAO,GAAG,8EAA8E,CAAC;4BAC/F,MAAM,KAAK,CAAC;yBACZ;wBACD,qBAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,EAAA;;wBAAtC,SAAsC,CAAC;wBACvC,sBAAO,OAAO,EAAC;;;;KACf;IACF,iBAAC;AAAD,CAAC,AAxED,IAwEC;AAxEY,gCAAU"}