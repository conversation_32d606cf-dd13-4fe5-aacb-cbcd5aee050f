"use strict";
/**
 * -------------------------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation.  All Rights Reserved.  Licensed under the MIT License.
 * See License in the project root for license information.
 * -------------------------------------------------------------------------------------------
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenCredentialAuthenticationProvider = void 0;
var tslib_1 = require("tslib");
var GraphClientError_1 = require("../../GraphClientError");
/**
 * @module TokenCredentialAuthenticationProvider
 */
/**
 * @class
 * Class representing TokenCredentialAuthenticationProvider
 * This feature is introduced in Version 3.0.0
 * @extends AuthenticationProvider
 * Reference - https://github.com/Azure/azure-sdk-for-js/blob/master/sdk/identity/identity/README.md
 */
var TokenCredentialAuthenticationProvider = /** @class */ (function () {
    /**
     * @public
     * @constructor
     * Creates an instance of TokenCredentialAuthenticationProvider
     * @param {TokenCredential} tokenCredential - An instance of @azure/identity TokenCredential
     * @param {TokenCredentialAuthenticationProviderOptions} authenticationProviderOptions - An instance of TokenCredentialAuthenticationProviderOptions
     * @returns An instance of TokenCredentialAuthenticationProvider
     */
    function TokenCredentialAuthenticationProvider(tokenCredential, authenticationProviderOptions) {
        if (!tokenCredential) {
            throw new GraphClientError_1.GraphClientError("Please pass a token credential object to the TokenCredentialAuthenticationProvider class constructor");
        }
        if (!authenticationProviderOptions) {
            throw new GraphClientError_1.GraphClientError("Please pass the TokenCredentialAuthenticationProviderOptions with scopes to the TokenCredentialAuthenticationProvider class constructor");
        }
        this.authenticationProviderOptions = authenticationProviderOptions;
        this.tokenCredential = tokenCredential;
    }
    /**
     * @public
     * @async
     * To get the access token
     * @param {TokenCredentialAuthenticationProviderOptions} authenticationProviderOptions - The authentication provider options object
     * @returns The promise that resolves to an access token
     */
    TokenCredentialAuthenticationProvider.prototype.getAccessToken = function () {
        return (0, tslib_1.__awaiter)(this, void 0, void 0, function () {
            var scopes, error, response;
            return (0, tslib_1.__generator)(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        scopes = this.authenticationProviderOptions.scopes;
                        error = new GraphClientError_1.GraphClientError();
                        if (!scopes || scopes.length === 0) {
                            error.name = "Empty Scopes";
                            error.message = "Scopes cannot be empty, Please provide scopes";
                            throw error;
                        }
                        return [4 /*yield*/, this.tokenCredential.getToken(scopes, this.authenticationProviderOptions.getTokenoptions)];
                    case 1:
                        response = _a.sent();
                        if (response) {
                            return [2 /*return*/, response.token];
                        }
                        error.message = "Cannot retrieve accessToken from the Token Credential object";
                        error.name = "Access token is undefined";
                        throw error;
                }
            });
        });
    };
    return TokenCredentialAuthenticationProvider;
}());
exports.TokenCredentialAuthenticationProvider = TokenCredentialAuthenticationProvider;
//# sourceMappingURL=TokenCredentialAuthenticationProvider.js.map