{"version": 3, "file": "TokenCredentialAuthenticationProvider.js", "sourceRoot": "", "sources": ["../../../../src/authentication/azureTokenCredentials/TokenCredentialAuthenticationProvider.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;AAIH,2DAA0D;AAI1D;;GAEG;AAEH;;;;;;GAMG;AACH;IAaC;;;;;;;OAOG;IACH,+CAAmB,eAAgC,EAAE,6BAA2E;QAC/H,IAAI,CAAC,eAAe,EAAE;YACrB,MAAM,IAAI,mCAAgB,CAAC,sGAAsG,CAAC,CAAC;SACnI;QACD,IAAI,CAAC,6BAA6B,EAAE;YACnC,MAAM,IAAI,mCAAgB,CAAC,yIAAyI,CAAC,CAAC;SACtK;QACD,IAAI,CAAC,6BAA6B,GAAG,6BAA6B,CAAC;QACnE,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;IACxC,CAAC;IAED;;;;;;OAMG;IACU,8DAAc,GAA3B;;;;;;wBACO,MAAM,GAAG,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC;wBACnD,KAAK,GAAG,IAAI,mCAAgB,EAAE,CAAC;wBAErC,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;4BACnC,KAAK,CAAC,IAAI,GAAG,cAAc,CAAC;4BAC5B,KAAK,CAAC,OAAO,GAAG,+CAA+C,CAAC;4BAChE,MAAM,KAAK,CAAC;yBACZ;wBACgB,qBAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC,EAAA;;wBAA1G,QAAQ,GAAG,SAA+F;wBAChH,IAAI,QAAQ,EAAE;4BACb,sBAAO,QAAQ,CAAC,KAAK,EAAC;yBACtB;wBACD,KAAK,CAAC,OAAO,GAAG,8DAA8D,CAAC;wBAC/E,KAAK,CAAC,IAAI,GAAG,2BAA2B,CAAC;wBACzC,MAAM,KAAK,CAAC;;;;KACZ;IACF,4CAAC;AAAD,CAAC,AAxDD,IAwDC;AAxDY,sFAAqC"}