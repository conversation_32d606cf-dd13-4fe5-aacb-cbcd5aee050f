"use strict";
/**
 * -------------------------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation.  All Rights Reserved.  Licensed under the MIT License.
 * See License in the project root for license information.
 * -------------------------------------------------------------------------------------------
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthCodeMSALBrowserAuthenticationProvider = void 0;
var tslib_1 = require("tslib");
/**
 * @module AuthCodeMSALBrowserAuthenticationProvider
 */
var msal_browser_1 = require("@azure/msal-browser");
var GraphClientError_1 = require("../../GraphClientError");
/**
 * an AuthenticationProvider implementation supporting msal-browser library.
 * This feature is introduced in Version 3.0.0
 * @class
 * @extends AuthenticationProvider
 */
var AuthCodeMSALBrowserAuthenticationProvider = /** @class */ (function () {
    /**
     * @public
     * @constructor
     * Creates an instance of ImplicitMSALAuthenticationProvider
     * @param {PublicClientApplication} msalApplication - An instance of MSAL PublicClientApplication
     * @param {AuthCodeMSALBrowserAuthenticationProviderOptions} options - An instance of MSALAuthenticationProviderOptions
     * @returns An instance of ImplicitMSALAuthenticationProvider
     */
    function AuthCodeMSALBrowserAuthenticationProvider(publicClientApplication, options) {
        this.publicClientApplication = publicClientApplication;
        this.options = options;
        if (!options || !publicClientApplication) {
            throw new GraphClientError_1.GraphClientError("Please pass valid PublicClientApplication instance and AuthCodeMSALBrowserAuthenticationProviderOptions instance to instantiate MSALBrowserAuthenticationProvider");
        }
    }
    /**
     * @public
     * @async
     * To get the access token for the request
     * @returns The promise that resolves to an access token
     */
    AuthCodeMSALBrowserAuthenticationProvider.prototype.getAccessToken = function () {
        return (0, tslib_1.__awaiter)(this, void 0, void 0, function () {
            var scopes, account, error, response, error_1, response;
            return (0, tslib_1.__generator)(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        scopes = this.options && this.options.scopes;
                        account = this.options && this.options.account;
                        error = new GraphClientError_1.GraphClientError();
                        if (!scopes || scopes.length === 0) {
                            error.name = "Empty Scopes";
                            error.message = "Scopes cannot be empty, Please provide scopes";
                            throw error;
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 9]);
                        return [4 /*yield*/, this.publicClientApplication.acquireTokenSilent({
                                scopes: scopes,
                                account: account,
                            })];
                    case 2:
                        response = _a.sent();
                        if (!response || !response.accessToken) {
                            error.name = "Access token is undefined";
                            error.message = "Received empty access token from PublicClientApplication";
                            throw error;
                        }
                        return [2 /*return*/, response.accessToken];
                    case 3:
                        error_1 = _a.sent();
                        if (!(error_1 instanceof msal_browser_1.InteractionRequiredAuthError)) return [3 /*break*/, 7];
                        if (!(this.options.interactionType === msal_browser_1.InteractionType.Redirect)) return [3 /*break*/, 4];
                        this.publicClientApplication.acquireTokenRedirect({ scopes: scopes });
                        return [3 /*break*/, 6];
                    case 4:
                        if (!(this.options.interactionType === msal_browser_1.InteractionType.Popup)) return [3 /*break*/, 6];
                        return [4 /*yield*/, this.publicClientApplication.acquireTokenPopup({ scopes: scopes })];
                    case 5:
                        response = _a.sent();
                        return [2 /*return*/, response.accessToken];
                    case 6: return [3 /*break*/, 8];
                    case 7: throw error_1;
                    case 8: return [3 /*break*/, 9];
                    case 9: return [2 /*return*/];
                }
            });
        });
    };
    return AuthCodeMSALBrowserAuthenticationProvider;
}());
exports.AuthCodeMSALBrowserAuthenticationProvider = AuthCodeMSALBrowserAuthenticationProvider;
//# sourceMappingURL=AuthCodeMSALBrowserAuthenticationProvider.js.map