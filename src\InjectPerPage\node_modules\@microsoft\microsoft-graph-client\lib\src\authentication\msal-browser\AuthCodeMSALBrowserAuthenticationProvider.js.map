{"version": 3, "file": "AuthCodeMSALBrowserAuthenticationProvider.js", "sourceRoot": "", "sources": ["../../../../src/authentication/msal-browser/AuthCodeMSALBrowserAuthenticationProvider.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;AAEH;;GAEG;AAEH,oDAAmI;AAEnI,2DAA0D;AAI1D;;;;;GAKG;AACH;IACC;;;;;;;OAOG;IACH,mDAA2B,uBAAgD,EAAU,OAAyD;QAAnH,4BAAuB,GAAvB,uBAAuB,CAAyB;QAAU,YAAO,GAAP,OAAO,CAAkD;QAC7I,IAAI,CAAC,OAAO,IAAI,CAAC,uBAAuB,EAAE;YACzC,MAAM,IAAI,mCAAgB,CAAC,mKAAmK,CAAC,CAAC;SAChM;IACF,CAAC;IAED;;;;;OAKG;IACU,kEAAc,GAA3B;;;;;;wBACO,MAAM,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;wBAC7C,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;wBAC/C,KAAK,GAAG,IAAI,mCAAgB,EAAE,CAAC;wBACrC,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;4BACnC,KAAK,CAAC,IAAI,GAAG,cAAc,CAAC;4BAC5B,KAAK,CAAC,OAAO,GAAG,+CAA+C,CAAC;4BAChE,MAAM,KAAK,CAAC;yBACZ;;;;wBAEuC,qBAAM,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,CAAC;gCAC5F,MAAM,QAAA;gCACN,OAAO,SAAA;6BACP,CAAC,EAAA;;wBAHI,QAAQ,GAAyB,SAGrC;wBACF,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;4BACvC,KAAK,CAAC,IAAI,GAAG,2BAA2B,CAAC;4BACzC,KAAK,CAAC,OAAO,GAAG,0DAA0D,CAAC;4BAC3E,MAAM,KAAK,CAAC;yBACZ;wBACD,sBAAO,QAAQ,CAAC,WAAW,EAAC;;;6BAExB,CAAA,OAAK,YAAY,2CAA4B,CAAA,EAA7C,wBAA6C;6BAC5C,CAAA,IAAI,CAAC,OAAO,CAAC,eAAe,KAAK,8BAAe,CAAC,QAAQ,CAAA,EAAzD,wBAAyD;wBAC5D,IAAI,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,EAAE,MAAM,QAAA,EAAE,CAAC,CAAC;;;6BACpD,CAAA,IAAI,CAAC,OAAO,CAAC,eAAe,KAAK,8BAAe,CAAC,KAAK,CAAA,EAAtD,wBAAsD;wBACzB,qBAAM,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,EAAE,MAAM,QAAA,EAAE,CAAC,EAAA;;wBAAjG,QAAQ,GAAyB,SAAgE;wBACvG,sBAAO,QAAQ,CAAC,WAAW,EAAC;;4BAG7B,MAAM,OAAK,CAAC;;;;;;KAGd;IACF,gDAAC;AAAD,CAAC,AAtDD,IAsDC;AAtDY,8FAAyC"}