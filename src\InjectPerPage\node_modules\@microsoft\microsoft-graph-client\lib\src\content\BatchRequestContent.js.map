{"version": 3, "file": "BatchRequestContent.js", "sourceRoot": "", "sources": ["../../../src/content/BatchRequestContent.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;AAEH;;GAEG;AACH,kDAAiD;AAkEjD;;;GAGG;AACH;IAqMC;;;;;;OAMG;IACH,6BAAmB,QAA6B;QAC/C,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,EAAE,CAAC;QAC1B,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;YACpC,IAAM,KAAK,GAAG,mBAAmB,CAAC,YAAY,CAAC;YAC/C,IAAI,QAAQ,CAAC,MAAM,GAAG,KAAK,EAAE;gBAC5B,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,8EAAuE,KAAK,CAAE,CAAC,CAAC;gBACxG,KAAK,CAAC,IAAI,GAAG,sBAAsB,CAAC;gBACpC,MAAM,KAAK,CAAC;aACZ;YACD,KAAkB,UAAQ,EAAR,qBAAQ,EAAR,sBAAQ,EAAR,IAAQ,EAAE;gBAAvB,IAAM,GAAG,iBAAA;gBACb,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;aACrB;SACD;IACF,CAAC;IA3MD;;;;;;;;;;;;;;;OAeG;IAEY,wCAAoB,GAAnC,UAAoC,QAAuC;QAC1E,IAAM,UAAU,GAAG,UAAC,IAAmC;YACtD,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAChC,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE;gBACjB,IAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC5B,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;oBAClE,OAAO,KAAK,CAAC;iBACb;gBACD,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;aACtB;YACD,OAAO,IAAI,CAAC;QACb,CAAC,CAAC;QACF,IAAM,QAAQ,GAAG,UAAC,IAAmC;YACpD,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAChC,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC1B,IAAM,YAAY,GAAqB,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACpD,IAAI,YAAY,CAAC,SAAS,KAAK,SAAS,IAAI,YAAY,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC9E,OAAO,KAAK,CAAC;aACb;YACD,IAAI,IAAI,GAAG,GAAG,CAAC;YACf,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE;gBACjB,IAAM,MAAM,GAAqB,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC9C,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;oBAChH,OAAO,KAAK,CAAC;iBACb;gBACD,IAAI,GAAG,GAAG,CAAC;gBACX,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;aACtB;YACD,OAAO,IAAI,CAAC;QACb,CAAC,CAAC;QACF,IAAM,MAAM,GAAG,UAAC,IAAmC;YAClD,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAChC,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC1B,IAAM,YAAY,GAAqB,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACpD,IAAI,YAAoB,CAAC;YACzB,IAAI,YAAY,CAAC,SAAS,KAAK,SAAS,IAAI,YAAY,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;gBAChF,YAAY,GAAG,YAAY,CAAC,EAAE,CAAC;aAC/B;iBAAM;gBACN,IAAI,YAAY,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;oBACxC,IAAM,aAAa,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBAChD,IAAI,aAAa,KAAK,YAAY,CAAC,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE;wBACjE,YAAY,GAAG,aAAa,CAAC;qBAC7B;yBAAM;wBACN,OAAO,KAAK,CAAC;qBACb;iBACD;qBAAM;oBACN,OAAO,KAAK,CAAC;iBACb;aACD;YACD,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE;gBACjB,IAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC5B,IAAI,CAAC,MAAM,CAAC,SAAS,KAAK,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC,IAAI,YAAY,KAAK,MAAM,CAAC,EAAE,EAAE;oBACpG,OAAO,KAAK,CAAC;iBACb;gBACD,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;oBACpE,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,KAAK,YAAY,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,YAAY,CAAC,EAAE;wBAC1G,OAAO,KAAK,CAAC;qBACb;oBACD,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;wBAChC,OAAO,KAAK,CAAC;qBACb;iBACD;gBACD,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;aACtB;YACD,OAAO,IAAI,CAAC;QACb,CAAC,CAAC;QACF,IAAI,QAAQ,CAAC,IAAI,KAAK,CAAC,EAAE;YACxB,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;YACpF,KAAK,CAAC,IAAI,GAAG,sBAAsB,CAAC;YACpC,MAAM,KAAK,CAAC;SACZ;QACD,OAAO,UAAU,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC;IACvE,CAAC;IAED;;;;;;;OAOG;IACkB,kCAAc,GAAnC,UAAoC,OAA0B;;;;;;wBACvD,WAAW,GAAgB;4BAChC,GAAG,EAAE,EAAE;yBACP,CAAC;wBACI,YAAY,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC;wBAC9C,8CAA8C;wBAC9C,WAAW,CAAC,GAAG,GAAG,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC;wBAC5G,WAAW,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;wBAC9B,OAAO,GAAG,EAAE,CAAC;wBACnB,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,GAAG;4BAClC,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;wBACtB,CAAC,CAAC,CAAC;wBACH,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE;4BAChC,WAAW,CAAC,OAAO,GAAG,OAAO,CAAC;yBAC9B;6BACG,CAAA,OAAO,CAAC,MAAM,KAAK,6BAAa,CAAC,KAAK,IAAI,OAAO,CAAC,MAAM,KAAK,6BAAa,CAAC,IAAI,IAAI,OAAO,CAAC,MAAM,KAAK,6BAAa,CAAC,GAAG,CAAA,EAAvH,wBAAuH;wBAC1H,KAAA,WAAW,CAAA;wBAAQ,qBAAM,mBAAmB,CAAC,cAAc,CAAC,OAAO,CAAC,EAAA;;wBAApE,GAAY,IAAI,GAAG,SAAiD,CAAC;;;oBAEtE;;uBAEG;oBACH,sBAAO,WAAW,EAAC;;;;KACnB;IAED;;;;;;;OAOG;IACkB,kCAAc,GAAnC,UAAoC,OAA0B;;;;;;wBACzD,UAAU,GAAG,KAAK,CAAC;;;;wBAGhB,QAAQ,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;wBAC1B,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;wBAA5B,IAAI,GAAG,SAAqB,CAAC;wBAC7B,UAAU,GAAG,IAAI,CAAC;;;;;;6BAIf,CAAC,UAAU,EAAX,yBAAW;;;;6BAET,CAAA,OAAO,IAAI,KAAK,WAAW,CAAA,EAA3B,wBAA2B;wBACjB,qBAAM,OAAO,CAAC,IAAI,EAAE,EAAA;;wBAA3B,SAAO,SAAoB;wBAC3B,WAAS,IAAI,UAAU,EAAE,CAAC;wBACzB,qBAAM,IAAI,OAAO,CAAC,UAAC,OAAO;gCAChC,QAAM,CAAC,gBAAgB,CACtB,MAAM,EACN;oCACC,IAAM,OAAO,GAAG,QAAM,CAAC,MAAgB,CAAC;oCACxC;;;;;;;;uCAQG;oCACH,IAAM,KAAK,GAAG,IAAI,MAAM,CAAC,iDAAiD,CAAC,CAAC;oCAC5E,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oCACrC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;gCACtB,CAAC,EACD,KAAK,CACL,CAAC;gCACF,QAAM,CAAC,aAAa,CAAC,MAAI,CAAC,CAAC;4BAC5B,CAAC,CAAC,EAAA;;wBArBF,IAAI,GAAG,SAqBL,CAAC;;;6BACO,CAAA,OAAO,MAAM,KAAK,WAAW,CAAA,EAA7B,yBAA6B;wBACxB,qBAAM,OAAO,CAAC,MAAM,EAAE,EAAA;;wBAA/B,MAAM,GAAG,SAAsB;wBACrC,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;;;wBAElC,UAAU,GAAG,IAAI,CAAC;;;;;6BAKpB,sBAAO,IAAI,EAAC;;;;KACZ;IAwBD;;;;;OAKG;IACI,wCAAU,GAAjB,UAAkB,OAAyB;QAC1C,IAAM,KAAK,GAAG,mBAAmB,CAAC,YAAY,CAAC;QAC/C,IAAI,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE;YACtB,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;YAClF,KAAK,CAAC,IAAI,GAAG,sBAAsB,CAAC;YACpC,MAAM,KAAK,CAAC;SACZ;QACD,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,KAAK,EAAE;YACjC,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,8EAAuE,KAAK,CAAE,CAAC,CAAC;YACxG,KAAK,CAAC,IAAI,GAAG,sBAAsB,CAAC;YACpC,MAAM,KAAK,CAAC;SACZ;QACD,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;YAClC,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,2CAAoC,OAAO,CAAC,EAAE,yCAAsC,CAAC,CAAC;YAC9G,KAAK,CAAC,IAAI,GAAG,2BAA2B,CAAC;YACzC,MAAM,KAAK,CAAC;SACZ;QACD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QACvC,OAAO,OAAO,CAAC,EAAE,CAAC;IACnB,CAAC;IAED;;;;;OAKG;IACI,2CAAa,GAApB,UAAqB,SAAiB;QACrC,IAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACrD,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;QACzC,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC1B;;WAEG;QACH,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE;YACjB,IAAM,YAAY,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAC5C,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;gBACxC,IAAM,KAAK,GAAG,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;gBAC9C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;oBACjB,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;iBAC9B;gBACD,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;oBAC9B,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;iBAC9B;aACD;YACD,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;SACtB;QACD,OAAO,YAAY,CAAC;IACrB,CAAC;IAED;;;;;OAKG;IACU,wCAAU,GAAvB;;;;;;wBACO,QAAQ,GAAuB,EAAE,CAAC;wBAClC,WAAW,GAAqB;4BACrC,QAAQ,UAAA;yBACR,CAAC;wBACI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;wBACrC,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;wBAC1B,IAAI,GAAG,CAAC,IAAI,EAAE;4BACP,KAAK,GAAG,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;4BACnF,KAAK,CAAC,IAAI,GAAG,eAAe,CAAC;4BAC7B,MAAM,KAAK,CAAC;yBACZ;wBACD,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;4BACvD,KAAK,GAAG,IAAI,KAAK,CAAC,sUAGqF,CAAC,CAAC;4BAC/G,KAAK,CAAC,IAAI,GAAG,oBAAoB,CAAC;4BAClC,MAAM,KAAK,CAAC;yBACZ;;;6BACM,CAAC,GAAG,CAAC,IAAI;wBACT,WAAW,GAAqB,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;wBACP,qBAAM,mBAAmB,CAAC,cAAc,CAAC,WAAW,CAAC,OAA4B,CAAC,EAAA;;wBAAxH,gBAAgB,GAAqB,CAAC,SAAkF,CAAqB;wBACnJ;;;;2BAIG;wBACH,IAAI,gBAAgB,CAAC,IAAI,KAAK,SAAS,IAAI,CAAC,gBAAgB,CAAC,OAAO,KAAK,SAAS,IAAI,gBAAgB,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,SAAS,CAAC,EAAE;4BACxI,KAAK,GAAG,IAAI,KAAK,CAAC,4DAAqD,WAAW,CAAC,EAAE,uEAAoE,CAAC,CAAC;4BACjK,KAAK,CAAC,IAAI,GAAG,6BAA6B,CAAC;4BAC3C,MAAM,KAAK,CAAC;yBACZ;wBACD,gBAAgB,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,CAAC;wBACrC,IAAI,WAAW,CAAC,SAAS,KAAK,SAAS,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;4BAC5E,gBAAgB,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;yBACnD;wBACD,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;wBAChC,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;;;wBAEvB,WAAW,CAAC,QAAQ,GAAG,QAAQ,CAAC;wBAChC,sBAAO,WAAW,EAAC;;;;KACnB;IAED;;;;;;OAMG;IACI,2CAAa,GAApB,UAAqB,WAAmB,EAAE,YAAqB;QAC9D,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;YACpC,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,oBAAa,WAAW,0CAAuC,CAAC,CAAC;YACzF,KAAK,CAAC,IAAI,GAAG,mBAAmB,CAAC;YACjC,MAAM,KAAK,CAAC;SACZ;QACD,IAAI,OAAO,YAAY,KAAK,WAAW,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;YAC5E,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,qBAAc,YAAY,0CAAuC,CAAC,CAAC;YAC3F,KAAK,CAAC,IAAI,GAAG,oBAAoB,CAAC;YAClC,MAAM,KAAK,CAAC;SACZ;QACD,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;YACxC,IAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YACjD,IAAI,SAAS,CAAC,SAAS,KAAK,SAAS,EAAE;gBACtC,SAAS,CAAC,SAAS,GAAG,EAAE,CAAC;aACzB;YACD,IAAI,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE;gBACrD,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,qBAAc,YAAY,+CAAqC,WAAW,CAAE,CAAC,CAAC;gBACtG,KAAK,CAAC,IAAI,GAAG,sBAAsB,CAAC;gBACpC,MAAM,KAAK,CAAC;aACZ;YACD,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SACvC;aAAM;YACN,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;YACzC,IAAI,IAAI,SAAA,CAAC;YACT,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,WAAW,EAAE;gBACpD,IAAI,GAAG,GAAG,CAAC;gBACX,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;aACtB;YACD,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;gBAChC,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC1B,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,KAAK,SAAS,EAAE;oBACzC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,EAAE,CAAC;iBAC5B;gBACD,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;oBAC/C,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,qBAAc,GAAG,+CAAqC,WAAW,CAAE,CAAC,CAAC;oBAC7F,KAAK,CAAC,IAAI,GAAG,sBAAsB,CAAC;oBACpC,MAAM,KAAK,CAAC;iBACZ;gBACD,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACjC;iBAAM;gBACN,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,+BAAwB,YAAY,qDAAkD,CAAC,CAAC;gBAChH,KAAK,CAAC,IAAI,GAAG,6BAA6B,CAAC;gBAC3C,MAAM,KAAK,CAAC;aACZ;SACD;IACF,CAAC;IAED;;;;;;OAMG;IACI,8CAAgB,GAAvB,UAAwB,WAAmB,EAAE,YAAqB;QACjE,IAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAC/C,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACxG,OAAO,KAAK,CAAC;SACb;QACD,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;YACxC,IAAM,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACtD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;gBACjB,OAAO,KAAK,CAAC;aACb;YACD,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACnC,OAAO,IAAI,CAAC;SACZ;aAAM;YACN,OAAO,OAAO,CAAC,SAAS,CAAC;YACzB,OAAO,IAAI,CAAC;SACZ;IACF,CAAC;IAnZD;;;;OAIG;IACY,gCAAY,GAAG,EAAE,CAAC;IA+YlC,0BAAC;CAAA,AArZD,IAqZC;AArZY,kDAAmB"}