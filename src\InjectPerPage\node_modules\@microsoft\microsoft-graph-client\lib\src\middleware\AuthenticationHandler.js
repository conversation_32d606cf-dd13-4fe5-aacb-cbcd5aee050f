"use strict";
/**
 * -------------------------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation.  All Rights Reserved.  Licensed under the MIT License.
 * See License in the project root for license information.
 * -------------------------------------------------------------------------------------------
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthenticationHandler = void 0;
var tslib_1 = require("tslib");
/**
 * @module AuthenticationHandler
 */
var GraphRequestUtil_1 = require("../GraphRequestUtil");
var MiddlewareControl_1 = require("./MiddlewareControl");
var MiddlewareUtil_1 = require("./MiddlewareUtil");
var AuthenticationHandlerOptions_1 = require("./options/AuthenticationHandlerOptions");
var TelemetryHandlerOptions_1 = require("./options/TelemetryHandlerOptions");
/**
 * @class
 * @implements Middleware
 * Class representing AuthenticationHandler
 */
var AuthenticationHandler = /** @class */ (function () {
    /**
     * @public
     * @constructor
     * Creates an instance of AuthenticationHandler
     * @param {AuthenticationProvider} authenticationProvider - The authentication provider for the authentication handler
     */
    function AuthenticationHandler(authenticationProvider) {
        this.authenticationProvider = authenticationProvider;
    }
    /**
     * @public
     * @async
     * To execute the current middleware
     * @param {Context} context - The context object of the request
     * @returns A Promise that resolves to nothing
     */
    AuthenticationHandler.prototype.execute = function (context) {
        return (0, tslib_1.__awaiter)(this, void 0, void 0, function () {
            var url, options, authenticationProvider, authenticationProviderOptions, token, bearerKey;
            return (0, tslib_1.__generator)(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        url = typeof context.request === "string" ? context.request : context.request.url;
                        if (!((0, GraphRequestUtil_1.isGraphURL)(url) || (context.customHosts && (0, GraphRequestUtil_1.isCustomHost)(url, context.customHosts)))) return [3 /*break*/, 2];
                        options = void 0;
                        if (context.middlewareControl instanceof MiddlewareControl_1.MiddlewareControl) {
                            options = context.middlewareControl.getMiddlewareOptions(AuthenticationHandlerOptions_1.AuthenticationHandlerOptions);
                        }
                        authenticationProvider = void 0;
                        authenticationProviderOptions = void 0;
                        if (options) {
                            authenticationProvider = options.authenticationProvider;
                            authenticationProviderOptions = options.authenticationProviderOptions;
                        }
                        if (!authenticationProvider) {
                            authenticationProvider = this.authenticationProvider;
                        }
                        return [4 /*yield*/, authenticationProvider.getAccessToken(authenticationProviderOptions)];
                    case 1:
                        token = _a.sent();
                        bearerKey = "Bearer ".concat(token);
                        (0, MiddlewareUtil_1.appendRequestHeader)(context.request, context.options, AuthenticationHandler.AUTHORIZATION_HEADER, bearerKey);
                        TelemetryHandlerOptions_1.TelemetryHandlerOptions.updateFeatureUsageFlag(context, TelemetryHandlerOptions_1.FeatureUsageFlag.AUTHENTICATION_HANDLER_ENABLED);
                        return [3 /*break*/, 3];
                    case 2:
                        if (context.options.headers) {
                            delete context.options.headers[AuthenticationHandler.AUTHORIZATION_HEADER];
                        }
                        _a.label = 3;
                    case 3: return [4 /*yield*/, this.nextMiddleware.execute(context)];
                    case 4: return [2 /*return*/, _a.sent()];
                }
            });
        });
    };
    /**
     * @public
     * To set the next middleware in the chain
     * @param {Middleware} next - The middleware instance
     * @returns Nothing
     */
    AuthenticationHandler.prototype.setNext = function (next) {
        this.nextMiddleware = next;
    };
    /**
     * @private
     * A member representing the authorization header name
     */
    AuthenticationHandler.AUTHORIZATION_HEADER = "Authorization";
    return AuthenticationHandler;
}());
exports.AuthenticationHandler = AuthenticationHandler;
//# sourceMappingURL=AuthenticationHandler.js.map