{"version": 3, "file": "AuthenticationHandler.js", "sourceRoot": "", "sources": ["../../../src/middleware/AuthenticationHandler.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;AAEH;;GAEG;AAEH,wDAA+D;AAK/D,yDAAwD;AACxD,mDAAuD;AACvD,uFAAsF;AACtF,6EAA8F;AAE9F;;;;GAIG;AACH;IAmBC;;;;;OAKG;IACH,+BAAmB,sBAA8C;QAChE,IAAI,CAAC,sBAAsB,GAAG,sBAAsB,CAAC;IACtD,CAAC;IAED;;;;;;OAMG;IACU,uCAAO,GAApB,UAAqB,OAAgB;;;;;;wBAC9B,GAAG,GAAG,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC;6BACpF,CAAA,IAAA,6BAAU,EAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,IAAA,+BAAY,EAAC,GAAG,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC,CAAA,EAAlF,wBAAkF;wBACjF,OAAO,SAA8B,CAAC;wBAC1C,IAAI,OAAO,CAAC,iBAAiB,YAAY,qCAAiB,EAAE;4BAC3D,OAAO,GAAG,OAAO,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,2DAA4B,CAAiC,CAAC;yBACvH;wBACG,sBAAsB,SAAwB,CAAC;wBAC/C,6BAA6B,SAA+B,CAAC;wBACjE,IAAI,OAAO,EAAE;4BACZ,sBAAsB,GAAG,OAAO,CAAC,sBAAsB,CAAC;4BACxD,6BAA6B,GAAG,OAAO,CAAC,6BAA6B,CAAC;yBACtE;wBACD,IAAI,CAAC,sBAAsB,EAAE;4BAC5B,sBAAsB,GAAG,IAAI,CAAC,sBAAsB,CAAC;yBACrD;wBACqB,qBAAM,sBAAsB,CAAC,cAAc,CAAC,6BAA6B,CAAC,EAAA;;wBAA1F,KAAK,GAAW,SAA0E;wBAC1F,SAAS,GAAG,iBAAU,KAAK,CAAE,CAAC;wBACpC,IAAA,oCAAmB,EAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,qBAAqB,CAAC,oBAAoB,EAAE,SAAS,CAAC,CAAC;wBAC7G,iDAAuB,CAAC,sBAAsB,CAAC,OAAO,EAAE,0CAAgB,CAAC,8BAA8B,CAAC,CAAC;;;wBAEzG,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE;4BAC5B,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,CAAC;yBAC3E;;4BAEK,qBAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,EAAA;4BAAjD,sBAAO,SAA0C,EAAC;;;;KAClD;IAED;;;;;OAKG;IACI,uCAAO,GAAd,UAAe,IAAgB;QAC9B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC5B,CAAC;IAvED;;;OAGG;IACY,0CAAoB,GAAG,eAAe,CAAC;IAoEvD,4BAAC;CAAA,AAzED,IAyEC;AAzEY,sDAAqB"}