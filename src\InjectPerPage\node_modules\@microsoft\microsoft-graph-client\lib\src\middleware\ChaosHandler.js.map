{"version": 3, "file": "ChaosHandler.js", "sourceRoot": "", "sources": ["../../../src/middleware/ChaosHandler.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;AASH,yDAAwD;AACxD,mDAAgD;AAChD,+DAA8E;AAC9E,qEAAoE;AACpE,yDAAwD;AAExD;;;;;GAKG;AACH;IAqBC;;;;;;;OAOG;IACH,sBAAmB,OAAwD,EAAE,SAA4C;QAAtG,wBAAA,EAAA,cAAmC,yCAAmB,EAAE;QAC1E,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC5B,CAAC;IAED;;;;;;;OAOG;IACK,4CAAqB,GAA7B,UAA8B,mBAAwC,EAAE,SAAiB,EAAE,WAAmB;QAC7G,IAAM,cAAc,GAAY,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,OAAO,EAAE,CAAC;QACvH,cAAc,CAAC,MAAM,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;QACnD,cAAc,CAAC,MAAM,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;QAC/C,cAAc,CAAC,MAAM,CAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC;QACtD,cAAc,CAAC,MAAM,CAAC,qBAAqB,EAAE,EAAE,CAAC,CAAC;QACjD,cAAc,CAAC,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QAC3C,cAAc,CAAC,MAAM,CAAC,2BAA2B,EAAE,EAAE,CAAC,CAAC;QAEvD,IAAI,mBAAmB,CAAC,UAAU,KAAK,GAAG,EAAE;YAC3C,iDAAiD;YACjD,cAAc,CAAC,MAAM,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;SAC1C;QAED,OAAO,cAAc,CAAC;IACvB,CAAC;IAED;;;;;;;OAOG;IACK,yCAAkB,GAA1B,UAA2B,mBAAwC,EAAE,SAAiB,EAAE,WAAmB;QAC1G,IAAI,mBAAmB,CAAC,YAAY,EAAE;YACrC,OAAO,mBAAmB,CAAC,YAAY,CAAC;SACxC;QACD,IAAI,IAAS,CAAC;QACd,IAAI,mBAAmB,CAAC,UAAU,IAAI,GAAG,EAAE;YAC1C,IAAM,WAAW,GAAW,iCAAc,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;YAC3E,IAAM,UAAU,GAAW,mBAAmB,CAAC,aAAa,CAAC;YAE7D,IAAI,GAAG;gBACN,KAAK,EAAE;oBACN,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,UAAU;oBACnB,UAAU,EAAE;wBACX,YAAY,EAAE,SAAS;wBACvB,IAAI,EAAE,WAAW;qBACjB;iBACD;aACD,CAAC;SACF;aAAM;YACN,IAAI,GAAG,EAAE,CAAC;SACV;QACD,OAAO,IAAI,CAAC;IACb,CAAC;IAED;;;;;OAKG;IACK,qCAAc,GAAtB,UAAuB,mBAAwC,EAAE,OAAgB;QAChF,IAAM,UAAU,GAAG,OAAO,CAAC,OAAiB,CAAC;QAC7C,IAAM,SAAS,GAAG,IAAA,6BAAY,GAAE,CAAC;QACjC,IAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QAC/B,IAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,EAAE,SAAS,EAAE,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC1G,IAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,EAAE,SAAS,EAAE,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;QACrG,IAAM,IAAI,GAAQ,EAAE,GAAG,EAAE,UAAU,EAAE,MAAM,EAAE,mBAAmB,CAAC,UAAU,EAAE,UAAU,EAAE,mBAAmB,CAAC,aAAa,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC;QACtJ,OAAO,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,OAAO,YAAY,KAAK,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,CAAC;IACvH,CAAC;IAED;;;;;;OAMG;IACW,kCAAW,GAAzB,UAA0B,mBAAwC,EAAE,OAAgB;;;;;wBACnF,IAAI,CAAC,aAAa,CAAC,mBAAmB,EAAE,OAAO,CAAC,OAAiB,EAAE,OAAO,CAAC,OAAO,CAAC,MAAuB,CAAC,CAAC;6BACxG,CAAA,CAAC,mBAAmB,CAAC,aAAa,KAAK,6BAAa,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,mBAAmB,CAAC,eAAe,CAAA,EAA7J,wBAA6J;wBAChK,IAAI,CAAC,cAAc,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;;;6BACxC,IAAI,CAAC,cAAc,EAAnB,wBAAmB;wBAC7B,qBAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,EAAA;;wBAA1C,SAA0C,CAAC;;;;;;KAE5C;IAED;;;;;OAKG;IACK,0CAAmB,GAA3B,UAA4B,aAA4B;QACvD,IAAM,eAAe,GAAa,mCAAgB,CAAC,aAAa,CAAa,CAAC;QAC9E,OAAO,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;IAC5E,CAAC;IAED;;;;;OAKG;IACK,qCAAc,GAAtB,UAAuB,SAAiB;QACvC,IAAM,OAAO,GAAG,oDAAoD,CAAC;QACrE,IAAI,WAAmB,CAAC;QACxB,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;YACrC,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;SACzC;QACD,OAAO,WAAW,CAAC;IACpB,CAAC;IAED;;;;;;OAMG;IACK,oCAAa,GAArB,UAAsB,mBAAwC,EAAE,UAAkB,EAAE,aAA4B;QAAhH,iBA+BC;QA9BA,IAAI,mBAAmB,CAAC,aAAa,KAAK,6BAAa,CAAC,MAAM,EAAE;YAC/D,IAAI,mBAAmB,CAAC,UAAU,KAAK,SAAS,EAAE;gBACjD,6FAA6F;gBAC7F,IAAM,aAAW,GAAW,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;gBAC5D,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,aAAW,CAAC,KAAK,SAAS,EAAE;oBAClD,sCAAsC;oBACtC,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,aAAW,CAAC,CAAC,GAAG,CAAC,aAAa,CAAC,KAAK,SAAS,EAAE;wBACrE,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,aAAW,CAAC,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;qBACpF;oBACD,qCAAqC;iBACrC;qBAAM;oBACN,uDAAuD;oBACvD,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAC,KAA0B,EAAE,GAAW;wBAC9D,IAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;wBACvC,IAAI,QAAQ,CAAC,IAAI,CAAC,aAAW,CAAC,EAAE;4BAC/B,IAAI,KAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,aAAa,CAAC,KAAK,SAAS,EAAE;gCAC7D,mBAAmB,CAAC,UAAU,GAAG,KAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;6BAC5E;4BACD,qCAAqC;yBACrC;oBACF,CAAC,CAAC,CAAC;iBACH;gBAED,mFAAmF;aACnF;SACD;aAAM;YACN,mCAAmC;YACnC,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;YACzE,qCAAqC;SACrC;IACF,CAAC;IAED;;;;;OAKG;IACK,iCAAU,GAAlB,UAAmB,OAAgB;QAClC,IAAI,OAA4B,CAAC;QACjC,IAAI,OAAO,CAAC,iBAAiB,YAAY,qCAAiB,EAAE;YAC3D,OAAO,GAAG,OAAO,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,yCAAmB,CAAwB,CAAC;SACrG;QACD,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;YACnC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,yCAAmB,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SACjE;QAED,OAAO,OAAO,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACU,8BAAO,GAApB,UAAqB,OAAgB;;;;;;wBAC9B,mBAAmB,GAAwB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;wBACnE,qBAAM,IAAI,CAAC,WAAW,CAAC,mBAAmB,EAAE,OAAO,CAAC,EAAA;4BAA3D,sBAAO,SAAoD,EAAC;;;;KAC5D;IAED;;;;;OAKG;IACI,8BAAO,GAAd,UAAe,IAAgB;QAC9B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC5B,CAAC;IACF,mBAAC;AAAD,CAAC,AArOD,IAqOC;AArOY,oCAAY"}