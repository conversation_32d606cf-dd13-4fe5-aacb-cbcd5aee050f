{"version": 3, "file": "MiddlewareUtil.js", "sourceRoot": "", "sources": ["../../../src/middleware/MiddlewareUtil.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;AAQH;;;;GAIG;AACI,IAAM,YAAY,GAAG;IAC3B,IAAI,IAAI,GAAG,EAAE,CAAC;IACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;QAC5B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE;YAChD,IAAI,IAAI,GAAG,CAAC;SACZ;QACD,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;KACpD;IACD,OAAO,IAAI,CAAC;AACb,CAAC,CAAC;AATW,QAAA,YAAY,gBASvB;AAEF;;;;;;;GAOG;AACI,IAAM,gBAAgB,GAAG,UAAC,OAAoB,EAAE,OAAiC,EAAE,GAAW;IACpG,IAAI,KAAK,GAAW,IAAI,CAAC;IACzB,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,YAAY,OAAO,EAAE;QACjE,KAAK,GAAI,OAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;KAC9C;SAAM,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE;QAC3E,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,OAAO,YAAY,OAAO,EAAE;YACzE,KAAK,GAAI,OAAO,CAAC,OAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SAC9C;aAAM,IAAI,OAAO,CAAC,OAAO,YAAY,KAAK,EAAE;YAC5C,IAAM,OAAO,GAAG,OAAO,CAAC,OAAqB,CAAC;YAC9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC/C,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;oBAC1B,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,MAAM;iBACN;aACD;SACD;aAAM,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;YAC9C,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;SAC7B;KACD;IACD,OAAO,KAAK,CAAC;AACd,CAAC,CAAC;AApBW,QAAA,gBAAgB,oBAoB3B;AAEF;;;;;;;;GAQG;AACI,IAAM,gBAAgB,GAAG,UAAC,OAAoB,EAAE,OAAiC,EAAE,GAAW,EAAE,KAAa;;IACnH,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,YAAY,OAAO,EAAE;QAChE,OAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;KAC7C;SAAM,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;QAC1C,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE;YAClC,OAAO,CAAC,OAAO,GAAG,IAAI,OAAO;gBAC5B,GAAC,GAAG,IAAG,KAAK;oBACX,CAAC;SACH;aAAM;YACN,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,OAAO,YAAY,OAAO,EAAE;gBACxE,OAAO,CAAC,OAAmB,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;aAC7C;iBAAM,IAAI,OAAO,CAAC,OAAO,YAAY,KAAK,EAAE;gBAC5C,IAAI,CAAC,GAAG,CAAC,CAAC;gBACV,IAAM,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;gBACjC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;oBAClB,IAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;oBAClC,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;wBACtB,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;wBAClB,MAAM;qBACN;iBACD;gBACD,IAAI,CAAC,KAAK,CAAC,EAAE;oBACX,OAAO,CAAC,OAAsB,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;iBACnD;aACD;iBAAM;gBACN,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,YAAI,GAAC,GAAG,IAAG,KAAK,MAAG,CAAC;aACjD;SACD;KACD;AACF,CAAC,CAAC;AA7BW,QAAA,gBAAgB,oBA6B3B;AAEF;;;;;;;;GAQG;AACI,IAAM,mBAAmB,GAAG,UAAC,OAAoB,EAAE,OAAiC,EAAE,GAAW,EAAE,KAAa;;IACtH,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,YAAY,OAAO,EAAE;QAChE,OAAmB,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;KAChD;SAAM,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;QAC1C,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE;YAClC,OAAO,CAAC,OAAO,GAAG,IAAI,OAAO;gBAC5B,GAAC,GAAG,IAAG,KAAK;oBACX,CAAC;SACH;aAAM;YACN,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,OAAO,YAAY,OAAO,EAAE;gBACxE,OAAO,CAAC,OAAmB,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;aAChD;iBAAM,IAAI,OAAO,CAAC,OAAO,YAAY,KAAK,EAAE;gBAC3C,OAAO,CAAC,OAAsB,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;aACnD;iBAAM,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE;gBACzC,OAAO,CAAC,OAAO,aAAK,GAAC,GAAG,IAAG,KAAK,KAAE,CAAC;aACnC;iBAAM,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;gBAC9C,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;aAC7B;iBAAM;gBACN,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,YAAK,KAAK,CAAE,CAAC;aACrC;SACD;KACD;AACF,CAAC,CAAC;AAtBW,QAAA,mBAAmB,uBAsB9B;AAEF;;;;;;GAMG;AACI,IAAM,sBAAsB,GAAG,UAAO,MAAc,EAAE,OAAgB;;;;;qBAC/D,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,EAAnC,wBAAmC;gBAAG,qBAAM,OAAO,CAAC,IAAI,EAAE,EAAA;;gBAApB,KAAA,SAAoB,CAAA;;oBAAG,qBAAM,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,EAAA;;gBAAhC,KAAA,SAAgC,CAAA;;;gBAApG,IAAI,KAAgG;gBAClG,MAAM,GAA0G,OAAO,OAAjH,EAAE,OAAO,GAAiG,OAAO,QAAxG,EAAE,QAAQ,GAAuF,OAAO,SAA9F,EAAE,cAAc,GAAuE,OAAO,eAA9E,EAAE,IAAI,GAAiE,OAAO,KAAxE,EAAE,WAAW,GAAoD,OAAO,YAA3D,EAAE,KAAK,GAA6C,OAAO,MAApD,EAAE,QAAQ,GAAmC,OAAO,SAA1C,EAAE,SAAS,GAAwB,OAAO,UAA/B,EAAE,SAAS,GAAa,OAAO,UAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;gBAChI,sBAAO,IAAI,OAAO,CAAC,MAAM,EAAE,EAAE,MAAM,QAAA,EAAE,OAAO,SAAA,EAAE,IAAI,MAAA,EAAE,QAAQ,UAAA,EAAE,cAAc,gBAAA,EAAE,IAAI,MAAA,EAAE,WAAW,aAAA,EAAE,KAAK,OAAA,EAAE,QAAQ,UAAA,EAAE,SAAS,WAAA,EAAE,SAAS,WAAA,EAAE,MAAM,QAAA,EAAE,CAAC,EAAC;;;KAClJ,CAAC;AAJW,QAAA,sBAAsB,0BAIjC"}