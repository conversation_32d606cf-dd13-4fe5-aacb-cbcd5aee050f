{"version": 3, "file": "RedirectHandler.js", "sourceRoot": "", "sources": ["../../../src/middleware/RedirectHandler.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;AAOH,kDAAiD;AAEjD,yDAAwD;AACxD,mDAA0D;AAC1D,2EAA0E;AAC1E,6EAA8F;AAE9F;;;;;GAKG;AACH;IAsDC;;;;;;OAMG;IAEH,yBAAmB,OAA8D;QAA9D,wBAAA,EAAA,cAAsC,+CAAsB,EAAE;QAChF,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACxB,CAAC;IAED;;;;;OAKG;IACK,oCAAU,GAAlB,UAAmB,QAAkB;QACpC,OAAO,eAAe,CAAC,qBAAqB,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IAC9E,CAAC;IAED;;;;;OAKG;IACK,2CAAiB,GAAzB,UAA0B,QAAkB;QAC3C,OAAO,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;IAC9D,CAAC;IAED;;;;;OAKG;IACK,2CAAiB,GAAzB,UAA0B,QAAkB;QAC3C,OAAO,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;IAC9D,CAAC;IAED;;;;;OAKG;IACK,uCAAa,GAArB,UAAsB,GAAW;QAChC,OAAO,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;IAClC,CAAC;IAED;;;;;;OAMG;IACK,uDAA6B,GAArC,UAAsC,UAAkB,EAAE,WAAmB;QAC5E,IAAM,eAAe,GAAG,8BAA8B,CAAC;QACvD,IAAM,cAAc,GAAa,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAClE,IAAI,gBAAwB,CAAC;QAC7B,IAAI,iBAAyB,CAAC;QAC9B,IAAI,cAAc,KAAK,IAAI,EAAE;YAC5B,gBAAgB,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;SACrC;QACD,IAAM,eAAe,GAAa,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACpE,IAAI,eAAe,KAAK,IAAI,EAAE;YAC7B,iBAAiB,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;SACvC;QACD,OAAO,OAAO,gBAAgB,KAAK,WAAW,IAAI,OAAO,iBAAiB,KAAK,WAAW,IAAI,gBAAgB,KAAK,iBAAiB,CAAC;IACtI,CAAC;IAED;;;;;;;OAOG;IACW,0CAAgB,GAA9B,UAA+B,WAAmB,EAAE,OAAgB;;;;;;wBACnE,KAAA,OAAO,CAAA;6BAAW,CAAA,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ,CAAA,EAAnC,wBAAmC;wBAAG,KAAA,WAAW,CAAA;;4BAAG,qBAAM,IAAA,uCAAsB,EAAC,WAAW,EAAE,OAAO,CAAC,OAAkB,CAAC,EAAA;;wBAArE,KAAA,SAAqE,CAAA;;;wBAA3I,GAAQ,OAAO,KAA4H,CAAC;;;;;KAC5I;IAED;;;;;OAKG;IACK,oCAAU,GAAlB,UAAmB,OAAgB;QAClC,IAAI,OAA+B,CAAC;QACpC,IAAI,OAAO,CAAC,iBAAiB,YAAY,qCAAiB,EAAE;YAC3D,OAAO,GAAG,OAAO,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,+CAAsB,CAA2B,CAAC;SAC3G;QACD,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;YACnC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,+CAAsB,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SACpE;QACD,OAAO,OAAO,CAAC;IAChB,CAAC;IAED;;;;;;;;OAQG;IACW,6CAAmB,GAAjC,UAAkC,OAAgB,EAAE,aAAqB,EAAE,OAA+B;;;;;4BACzG,qBAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,EAAA;;wBAA1C,SAA0C,CAAC;wBACrC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;6BAC9B,CAAA,aAAa,GAAG,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA,EAAzI,wBAAyI;wBAC5I,EAAE,aAAa,CAAC;6BACZ,CAAA,QAAQ,CAAC,MAAM,KAAK,eAAe,CAAC,qBAAqB,CAAA,EAAzD,wBAAyD;wBAC5D,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,6BAAa,CAAC,GAAG,CAAC;wBAC3C,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;;;wBAEtB,WAAW,GAAW,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;wBAC7D,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,6BAA6B,CAAC,QAAQ,CAAC,GAAG,EAAE,WAAW,CAAC,EAAE;4BACtG,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC;yBACrE;wBACD,qBAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,OAAO,CAAC,EAAA;;wBAAjD,SAAiD,CAAC;;4BAEnD,qBAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,aAAa,EAAE,OAAO,CAAC,EAAA;;wBAA/D,SAA+D,CAAC;;4BAEhE,sBAAO;;;;;KAER;IAED;;;;;;OAMG;IACU,iCAAO,GAApB,UAAqB,OAAgB;;;;;;wBAC9B,aAAa,GAAG,CAAC,CAAC;wBAClB,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;wBACzC,OAAO,CAAC,OAAO,CAAC,QAAQ,GAAG,eAAe,CAAC,eAAe,CAAC;wBAC3D,iDAAuB,CAAC,sBAAsB,CAAC,OAAO,EAAE,0CAAgB,CAAC,wBAAwB,CAAC,CAAC;wBAC5F,qBAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,aAAa,EAAE,OAAO,CAAC,EAAA;4BAAtE,sBAAO,SAA+D,EAAC;;;;KACvE;IAED;;;;;OAKG;IACI,iCAAO,GAAd,UAAe,IAAgB;QAC9B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC5B,CAAC;IAjND;;;;OAIG;IACY,qCAAqB,GAAa;QAChD,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG,EAAE,oBAAoB;KACzB,CAAC;IAEF;;;;OAIG;IACY,qCAAqB,GAAG,GAAG,CAAC;IAE3C;;;;OAIG;IACY,+BAAe,GAAG,UAAU,CAAC;IAE5C;;;;OAIG;IACY,oCAAoB,GAAG,eAAe,CAAC;IAEtD;;;;OAIG;IACY,+BAAe,GAAoB,QAAQ,CAAC;IA2K5D,sBAAC;CAAA,AAnND,IAmNC;AAnNY,0CAAe"}