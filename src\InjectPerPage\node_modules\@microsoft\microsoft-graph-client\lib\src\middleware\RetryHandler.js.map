{"version": 3, "file": "RetryHandler.js", "sourceRoot": "", "sources": ["../../../src/middleware/RetryHandler.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;AAQH,kDAAiD;AAEjD,yDAAwD;AACxD,mDAAsE;AACtE,qEAAoE;AACpE,6EAA8F;AAE9F;;;;GAIG;AACH;IAsCC;;;;;;OAMG;IACH,sBAAmB,OAAwD;QAAxD,wBAAA,EAAA,cAAmC,yCAAmB,EAAE;QAC1E,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACxB,CAAC;IAED;;;;;;OAMG;IACK,8BAAO,GAAf,UAAgB,QAAkB;QACjC,OAAO,YAAY,CAAC,kBAAkB,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IACxE,CAAC;IAED;;;;;;OAMG;IACK,iCAAU,GAAlB,UAAmB,OAAoB,EAAE,OAAiC;QACzE,IAAM,MAAM,GAAG,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAE,OAAmB,CAAC,MAAM,CAAC;QAC1F,IAAM,gBAAgB,GAAY,MAAM,KAAK,6BAAa,CAAC,GAAG,IAAI,MAAM,KAAK,6BAAa,CAAC,KAAK,IAAI,MAAM,KAAK,6BAAa,CAAC,IAAI,CAAC;QAClI,IAAI,gBAAgB,EAAE;YACrB,IAAM,QAAQ,GAAG,IAAA,iCAAgB,EAAC,OAAO,EAAE,OAAO,EAAE,cAAc,CAAC,KAAK,0BAA0B,CAAC;YACnG,IAAI,QAAQ,EAAE;gBACb,OAAO,KAAK,CAAC;aACb;SACD;QACD,OAAO,IAAI,CAAC;IACb,CAAC;IAED;;;;;;;OAOG;IACK,+BAAQ,GAAhB,UAAiB,QAAkB,EAAE,aAAqB,EAAE,KAAa;QACxE,IAAM,aAAa,GAAG,cAAM,OAAA,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAhC,CAAgC,CAAC;QAC7D,IAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACjH,IAAI,QAAgB,CAAC;QACrB,IAAI,UAAU,KAAK,IAAI,EAAE;YACxB,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE;gBACrC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;aAC5E;iBAAM;gBACN,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;aAC9B;SACD;aAAM;YACN,gDAAgD;YAChD,QAAQ,GAAG,aAAa,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,aAAa,CAAC,GAAG,KAAK,GAAG,aAAa,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,aAAa,EAAE,CAAC;SAClI;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,aAAa,EAAE,CAAC,CAAC;IACzE,CAAC;IAED;;;;;OAKG;IACK,gDAAyB,GAAjC,UAAkC,QAAgB;QACjD,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,SAAA,CAAC,EAAI,QAAQ,CAAA,GAAG,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC;IAED;;;;;;OAMG;IACW,4BAAK,GAAnB,UAAoB,YAAoB;;;;gBACjC,iBAAiB,GAAG,YAAY,GAAG,IAAI,CAAC;gBAC9C,sBAAO,IAAI,OAAO,CAAC,UAAC,OAAO,IAAK,OAAA,UAAU,CAAC,OAAO,EAAE,iBAAiB,CAAC,EAAtC,CAAsC,CAAC,EAAC;;;KACxE;IAEO,iCAAU,GAAlB,UAAmB,OAAgB;QAClC,IAAI,OAA4B,CAAC;QACjC,IAAI,OAAO,CAAC,iBAAiB,YAAY,qCAAiB,EAAE;YAC3D,OAAO,GAAG,OAAO,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAwB,CAAC;SAC1G;QACD,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;YACnC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,yCAAmB,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SACjE;QACD,OAAO,OAAO,CAAC;IAChB,CAAC;IAED;;;;;;;;OAQG;IACW,uCAAgB,GAA9B,UAA+B,OAAgB,EAAE,aAAqB,EAAE,OAA4B;;;;;4BACnG,qBAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,EAAA;;wBAA1C,SAA0C,CAAC;6BACvC,CAAA,aAAa,GAAG,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,aAAa,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAA,EAAlO,wBAAkO;wBACrO,EAAE,aAAa,CAAC;wBAChB,IAAA,iCAAgB,EAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,YAAY,CAAC,oBAAoB,EAAE,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC;wBAC1G,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,aAAa,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;wBAC5E,qBAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAA;;wBAAvB,SAAuB,CAAC;wBACjB,qBAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,aAAa,EAAE,OAAO,CAAC,EAAA;4BAAnE,sBAAO,SAA4D,EAAC;4BAEpE,sBAAO;;;;KAER;IAED;;;;;;OAMG;IACU,8BAAO,GAApB,UAAqB,OAAgB;;;;;;wBAC9B,aAAa,GAAG,CAAC,CAAC;wBAClB,OAAO,GAAwB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;wBAC9D,iDAAuB,CAAC,sBAAsB,CAAC,OAAO,EAAE,0CAAgB,CAAC,qBAAqB,CAAC,CAAC;wBACzF,qBAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,aAAa,EAAE,OAAO,CAAC,EAAA;4BAAnE,sBAAO,SAA4D,EAAC;;;;KACpE;IAED;;;;;OAKG;IACI,8BAAO,GAAd,UAAe,IAAgB;QAC9B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC5B,CAAC;IApLD;;;;OAIG;IACY,+BAAkB,GAAa;QAC7C,GAAG;QACH,GAAG;QACH,GAAG,EAAE,kBAAkB;KACvB,CAAC;IAEF;;;;OAIG;IACY,iCAAoB,GAAG,eAAe,CAAC;IAEtD;;;;OAIG;IACY,+BAAkB,GAAG,aAAa,CAAC;IA8JnD,mBAAC;CAAA,AAtLD,IAsLC;AAtLY,oCAAY"}