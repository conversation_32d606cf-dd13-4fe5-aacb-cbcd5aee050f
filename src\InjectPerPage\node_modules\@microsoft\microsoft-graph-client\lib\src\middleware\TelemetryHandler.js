"use strict";
/**
 * -------------------------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation.  All Rights Reserved.  Licensed under the MIT License.
 * See License in the project root for license information.
 * -------------------------------------------------------------------------------------------
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TelemetryHandler = void 0;
var tslib_1 = require("tslib");
/**
 * @module TelemetryHandler
 */
var GraphRequestUtil_1 = require("../GraphRequestUtil");
var Version_1 = require("../Version");
var MiddlewareControl_1 = require("./MiddlewareControl");
var MiddlewareUtil_1 = require("./MiddlewareUtil");
var TelemetryHandlerOptions_1 = require("./options/TelemetryHandlerOptions");
/**
 * @class
 * @implements Middleware
 * Class for TelemetryHandler
 */
var TelemetryHandler = /** @class */ (function () {
    function TelemetryHandler() {
    }
    /**
     * @public
     * @async
     * To execute the current middleware
     * @param {Context} context - The context object of the request
     * @returns A Promise that resolves to nothing
     */
    TelemetryHandler.prototype.execute = function (context) {
        return (0, tslib_1.__awaiter)(this, void 0, void 0, function () {
            var url, clientRequestId, sdkVersionValue, options, featureUsage;
            return (0, tslib_1.__generator)(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        url = typeof context.request === "string" ? context.request : context.request.url;
                        if ((0, GraphRequestUtil_1.isGraphURL)(url) || (context.customHosts && (0, GraphRequestUtil_1.isCustomHost)(url, context.customHosts))) {
                            clientRequestId = (0, MiddlewareUtil_1.getRequestHeader)(context.request, context.options, TelemetryHandler.CLIENT_REQUEST_ID_HEADER);
                            if (!clientRequestId) {
                                clientRequestId = (0, MiddlewareUtil_1.generateUUID)();
                                (0, MiddlewareUtil_1.setRequestHeader)(context.request, context.options, TelemetryHandler.CLIENT_REQUEST_ID_HEADER, clientRequestId);
                            }
                            sdkVersionValue = "".concat(TelemetryHandler.PRODUCT_NAME, "/").concat(Version_1.PACKAGE_VERSION);
                            options = void 0;
                            if (context.middlewareControl instanceof MiddlewareControl_1.MiddlewareControl) {
                                options = context.middlewareControl.getMiddlewareOptions(TelemetryHandlerOptions_1.TelemetryHandlerOptions);
                            }
                            if (options) {
                                featureUsage = options.getFeatureUsage();
                                sdkVersionValue += " (".concat(TelemetryHandler.FEATURE_USAGE_STRING, "=").concat(featureUsage, ")");
                            }
                            (0, MiddlewareUtil_1.appendRequestHeader)(context.request, context.options, TelemetryHandler.SDK_VERSION_HEADER, sdkVersionValue);
                        }
                        else {
                            // Remove telemetry headers if present during redirection.
                            delete context.options.headers[TelemetryHandler.CLIENT_REQUEST_ID_HEADER];
                            delete context.options.headers[TelemetryHandler.SDK_VERSION_HEADER];
                        }
                        return [4 /*yield*/, this.nextMiddleware.execute(context)];
                    case 1: return [2 /*return*/, _a.sent()];
                }
            });
        });
    };
    /**
     * @public
     * To set the next middleware in the chain
     * @param {Middleware} next - The middleware instance
     * @returns Nothing
     */
    TelemetryHandler.prototype.setNext = function (next) {
        this.nextMiddleware = next;
    };
    /**
     * @private
     * @static
     * A member holding the name of the client request id header
     */
    TelemetryHandler.CLIENT_REQUEST_ID_HEADER = "client-request-id";
    /**
     * @private
     * @static
     * A member holding the name of the sdk version header
     */
    TelemetryHandler.SDK_VERSION_HEADER = "SdkVersion";
    /**
     * @private
     * @static
     * A member holding the language prefix for the sdk version header value
     */
    TelemetryHandler.PRODUCT_NAME = "graph-js";
    /**
     * @private
     * @static
     * A member holding the key for the feature usage metrics
     */
    TelemetryHandler.FEATURE_USAGE_STRING = "featureUsage";
    return TelemetryHandler;
}());
exports.TelemetryHandler = TelemetryHandler;
//# sourceMappingURL=TelemetryHandler.js.map