{"version": 3, "file": "TelemetryHandler.js", "sourceRoot": "", "sources": ["../../../src/middleware/TelemetryHandler.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;AAEH;;GAEG;AACH,wDAA+D;AAE/D,sCAA6C;AAE7C,yDAAwD;AACxD,mDAAyG;AACzG,6EAA4E;AAE5E;;;;GAIG;AACH;IAAA;IA+EA,CAAC;IA5CA;;;;;;OAMG;IACU,kCAAO,GAApB,UAAqB,OAAgB;;;;;;wBAC9B,GAAG,GAAG,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC;wBACxF,IAAI,IAAA,6BAAU,EAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,IAAA,+BAAY,EAAC,GAAG,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE;4BAGnF,eAAe,GAAW,IAAA,iCAAgB,EAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;4BAC5H,IAAI,CAAC,eAAe,EAAE;gCACrB,eAAe,GAAG,IAAA,6BAAY,GAAE,CAAC;gCACjC,IAAA,iCAAgB,EAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,gBAAgB,CAAC,wBAAwB,EAAE,eAAe,CAAC,CAAC;6BAC/G;4BACG,eAAe,GAAG,UAAG,gBAAgB,CAAC,YAAY,cAAI,yBAAe,CAAE,CAAC;4BACxE,OAAO,SAAyB,CAAC;4BACrC,IAAI,OAAO,CAAC,iBAAiB,YAAY,qCAAiB,EAAE;gCAC3D,OAAO,GAAG,OAAO,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,iDAAuB,CAA4B,CAAC;6BAC7G;4BACD,IAAI,OAAO,EAAE;gCACN,YAAY,GAAW,OAAO,CAAC,eAAe,EAAE,CAAC;gCACvD,eAAe,IAAI,YAAK,gBAAgB,CAAC,oBAAoB,cAAI,YAAY,MAAG,CAAC;6BACjF;4BACD,IAAA,oCAAmB,EAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,gBAAgB,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC;yBAC5G;6BAAM;4BACN,0DAA0D;4BAC1D,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;4BAC1E,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;yBACpE;wBACM,qBAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,EAAA;4BAAjD,sBAAO,SAA0C,EAAC;;;;KAClD;IAED;;;;;OAKG;IACI,kCAAO,GAAd,UAAe,IAAgB;QAC9B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC5B,CAAC;IA7ED;;;;OAIG;IACY,yCAAwB,GAAG,mBAAmB,CAAC;IAE9D;;;;OAIG;IACY,mCAAkB,GAAG,YAAY,CAAC;IAEjD;;;;OAIG;IACY,6BAAY,GAAG,UAAU,CAAC;IAEzC;;;;OAIG;IACY,qCAAoB,GAAG,cAAc,CAAC;IAoDtD,uBAAC;CAAA,AA/ED,IA+EC;AA/EY,4CAAgB"}