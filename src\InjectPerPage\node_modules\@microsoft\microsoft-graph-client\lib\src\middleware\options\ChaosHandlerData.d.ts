/**
 * -------------------------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation.  All Rights Reserved.  Licensed under the MIT License.
 * See License in the project root for license information.
 * -------------------------------------------------------------------------------------------
 */
/**
 * @module ChaosHandlerData
 */
/**
 * Contains RequestMethod to corresponding array of possible status codes, used for Random mode
 */
export declare const methodStatusCode: {
    [key: string]: number[];
};
/**
 * Contains statusCode to statusMessage map
 */
export declare const httpStatusCode: {
    [key: number]: string;
};
