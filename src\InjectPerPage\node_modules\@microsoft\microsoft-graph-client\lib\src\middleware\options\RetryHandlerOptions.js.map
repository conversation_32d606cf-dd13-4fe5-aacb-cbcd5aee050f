{"version": 3, "file": "RetryHandlerOptions.js", "sourceRoot": "", "sources": ["../../../../src/middleware/options/RetryHandlerOptions.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;AAeH;;;;GAIG;AAEH;IAqDC;;;;;;;;OAQG;IACH,6BAAmB,KAAiD,EAAE,UAA4D,EAAE,WAAiE;QAAlL,sBAAA,EAAA,QAAgB,mBAAmB,CAAC,aAAa;QAAE,2BAAA,EAAA,aAAqB,mBAAmB,CAAC,mBAAmB;QAAE,4BAAA,EAAA,cAA2B,mBAAmB,CAAC,kBAAkB;QACpM,IAAI,KAAK,GAAG,mBAAmB,CAAC,SAAS,IAAI,UAAU,GAAG,mBAAmB,CAAC,eAAe,EAAE;YAC9F,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,uDAAgD,mBAAmB,CAAC,SAAS,kBAAQ,mBAAmB,CAAC,eAAe,CAAE,CAAC,CAAC;YACpJ,KAAK,CAAC,IAAI,GAAG,kBAAkB,CAAC;YAChC,MAAM,KAAK,CAAC;SACZ;aAAM,IAAI,KAAK,GAAG,mBAAmB,CAAC,SAAS,EAAE;YACjD,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,wCAAiC,mBAAmB,CAAC,SAAS,CAAE,CAAC,CAAC;YAC1F,KAAK,CAAC,IAAI,GAAG,kBAAkB,CAAC;YAChC,MAAM,KAAK,CAAC;SACZ;aAAM,IAAI,UAAU,GAAG,mBAAmB,CAAC,eAAe,EAAE;YAC5D,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,6CAAsC,mBAAmB,CAAC,eAAe,CAAE,CAAC,CAAC;YACrG,KAAK,CAAC,IAAI,GAAG,kBAAkB,CAAC;YAChC,MAAM,KAAK,CAAC;SACZ;aAAM,IAAI,KAAK,GAAG,CAAC,IAAI,UAAU,GAAG,CAAC,EAAE;YACvC,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;YACvE,KAAK,CAAC,IAAI,GAAG,sBAAsB,CAAC;YACpC,MAAM,KAAK,CAAC;SACZ;aAAM,IAAI,KAAK,GAAG,CAAC,EAAE;YACrB,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;YACxD,KAAK,CAAC,IAAI,GAAG,sBAAsB,CAAC;YACpC,MAAM,KAAK,CAAC;SACZ;aAAM,IAAI,UAAU,GAAG,CAAC,EAAE;YAC1B,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;YAC7D,KAAK,CAAC,IAAI,GAAG,sBAAsB,CAAC;YACpC,MAAM,KAAK,CAAC;SACZ;QACD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,mBAAmB,CAAC,SAAS,CAAC,CAAC;QAC5D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,mBAAmB,CAAC,eAAe,CAAC,CAAC;QAC5E,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IAChC,CAAC;IAED;;;;OAIG;IACI,yCAAW,GAAlB;QACC,OAAO,mBAAmB,CAAC,SAAS,CAAC;IACtC,CAAC;IAnGD;;;;OAIG;IACY,iCAAa,GAAG,CAAC,CAAC;IAEjC;;;;OAIG;IACY,uCAAmB,GAAG,CAAC,CAAC;IAEvC;;;;OAIG;IACY,6BAAS,GAAG,GAAG,CAAC;IAE/B;;;;OAIG;IACY,mCAAe,GAAG,EAAE,CAAC;IAoBpC;;;OAGG;IACY,sCAAkB,GAAgB,cAAM,OAAA,IAAI,EAAJ,CAAI,CAAC;IAkD7D,0BAAC;CAAA,AArGD,IAqGC;AArGY,kDAAmB"}