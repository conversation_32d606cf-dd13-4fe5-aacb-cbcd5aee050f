{"version": 3, "file": "StreamUpload.js", "sourceRoot": "", "sources": ["../../../../../src/tasks/FileUploadTask/FileObjectClasses/StreamUpload.ts"], "names": [], "mappings": ";;;;AAAA,8DAA6D;AAe7D;;;GAGG;AACH;IAQC,sBAA0B,OAAmB,EAAS,IAAY,EAAS,IAAY;QAA7D,YAAO,GAAP,OAAO,CAAY;QAAS,SAAI,GAAJ,IAAI,CAAQ;QAAS,SAAI,GAAJ,IAAI,CAAQ;QACtF,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE;YAC/B,MAAM,IAAI,mCAAgB,CAAC,mFAAmF,CAAC,CAAC;SAChH;IACF,CAAC;IAED;;;;;OAKG;IACU,gCAAS,GAAtB,UAAuB,KAAY;;;;;;wBAC9B,SAAS,GAAG,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC;wBAI9C,IAAI,GAAG,EAAE,CAAC;wBAEhB;;;;;;2BAMG;wBACH,IAAI,IAAI,CAAC,aAAa,EAAE;4BACvB,IAAI,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,EAAE;gCACvD,MAAM,IAAI,mCAAgB,CAAC,iHAAiH,CAAC,CAAC;6BAC9I;4BAED,IAAI,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,EAAE;gCACjD,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,CAAC;gCACrD,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,CAAC;gCAE3D,kEAAkE;gCAClE,IAAI,KAAK,CAAC,QAAQ,KAAK,gBAAgB,IAAI,KAAK,CAAC,QAAQ,KAAK,gBAAgB,EAAE;oCAC/E,sBAAO,IAAI,CAAC,aAAa,CAAC,SAAS,EAAC;iCACpC;gCAED;;;;;mCAKG;gCACH,IAAI,KAAK,CAAC,QAAQ,KAAK,gBAAgB,EAAE;oCACxC,sBAAO,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,EAAC;iCAC9E;gCAED;;;;;mCAKG;gCACH,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;gCAEpF,SAAS,GAAG,KAAK,CAAC,QAAQ,GAAG,gBAAgB,CAAC;6BAC9C;yBACD;6BAEG,CAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAA,EAArC,wBAAqC;6BACpC,CAAA,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,SAAS,CAAA,EAAxC,wBAAwC;wBAC3C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;;wBAExC,KAAA,CAAA,KAAA,IAAI,CAAA,CAAC,IAAI,CAAA;wBAAC,qBAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,EAAA;;wBAApD,cAAU,SAA0C,EAAC,CAAC;;;4BAGvD,MAAM,IAAI,mCAAgB,CAAC,yBAAyB,CAAC,CAAC;;wBAEjD,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;wBACxC,IAAI,CAAC,aAAa,GAAG,EAAE,SAAS,EAAE,WAAW,EAAE,KAAK,OAAA,EAAE,CAAC;wBAEvD,sBAAO,WAAW,EAAC;;;;KACnB;IAED;;;;;OAKG;IAEK,2CAAoB,GAA5B,UAA6B,IAAY;QAAzC,iBAiCC;QAhCA,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;YAClC,IAAM,MAAM,GAAG,EAAE,CAAC;YAClB,IAAI,SAAS,GAAG,IAAI,CAAC;YACrB,IAAI,MAAM,GAAG,CAAC,CAAC;YACf,KAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE;gBACtB,IAAI,SAAS,GAAG,CAAC,EAAE;oBAClB,OAAO,MAAM,CAAC,IAAI,mCAAgB,CAAC,iDAAiD,CAAC,CAAC,CAAC;iBACvF;YACF,CAAC,CAAC,CAAC;YACH,KAAI,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,EAAE;gBAC3B;;;mBAGG;gBACH,IAAI,KAAK,CAAC;gBACV,OAAO,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,IAAI,EAAE;oBACxE,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC;oBACvB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACnB,IAAI,SAAS,GAAG,CAAC,EAAE;wBAClB,SAAS,GAAG,IAAI,GAAG,MAAM,CAAC;qBAC1B;iBACD;gBAED,IAAI,MAAM,KAAK,IAAI,EAAE;oBACpB,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;iBACtC;gBAED,IAAI,CAAC,KAAI,CAAC,OAAO,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;oBAC5C,OAAO,MAAM,CAAC,IAAI,mCAAgB,CAAC,8DAA8D,CAAC,CAAC,CAAC;iBACpG;YACF,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACJ,CAAC;IACF,mBAAC;AAAD,CAAC,AA9HD,IA8HC;AA9HY,oCAAY"}