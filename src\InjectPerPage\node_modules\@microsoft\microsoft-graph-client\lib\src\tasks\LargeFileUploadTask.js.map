{"version": 3, "file": "LargeFileUploadTask.js", "sourceRoot": "", "sources": ["../../../src/tasks/LargeFileUploadTask.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;AAEH;;GAEG;AAEH,wDAAuD;AACvD,gEAA+D;AAE/D,gDAA+C;AAE/C,gDAA+C;AAC/C,8DAA6D;AAiE7D;;;GAGG;AACH;IA6DC;;;;;;;;;OASG;IACH,6BAAmB,MAAc,EAAE,IAAmB,EAAE,aAAqC,EAAE,OAAwC;QAAxC,wBAAA,EAAA,YAAwC;QAtEvI;;;WAGG;QACK,sBAAiB,GAAW,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;QAmEnD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACpB,MAAM,IAAI,mCAAgB,CAAC,iHAAiH,CAAC,CAAC;SAC9I;aAAM;YACN,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;SACjB;QACD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YACvB,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC;SAC3C;QAED,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,SAAS,GAAG,IAAI,aAAK,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;IAC3D,CAAC;IAlDD;;;;;;;;;;OAUG;IACiB,uCAAmB,GAAvC,UAAwC,MAAc,EAAE,UAAkB,EAAE,OAAY,EAAE,OAA4C;QAA5C,wBAAA,EAAA,YAA4C;;;;;4BACrH,qBAAM,MAAM;6BAC1B,GAAG,CAAC,UAAU,CAAC;6BACf,OAAO,CAAC,OAAO,CAAC;6BAChB,IAAI,CAAC,OAAO,CAAC,EAAA;;wBAHT,OAAO,GAAG,SAGD;wBACT,sBAAsB,GAA2B;4BACtD,GAAG,EAAE,OAAO,CAAC,SAAS;4BACtB,MAAM,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC;4BAC5C,WAAW,EAAE,KAAK;yBAClB,CAAC;wBACF,sBAAO,sBAAsB,EAAC;;;;KAC9B;IA8BD;;;;;OAKG;IACK,wCAAU,GAAlB,UAAmB,MAAgB;QAClC,IAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAC3B,IAAI,OAAO,QAAQ,KAAK,WAAW,IAAI,QAAQ,KAAK,EAAE,EAAE;YACvD,OAAO,IAAI,aAAK,EAAE,CAAC;SACnB;QACD,IAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACvC,IAAM,MAAM,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC3C,IAAI,MAAM,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACzC,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;YACzB,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;SAC5B;QACD,OAAO,IAAI,aAAK,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAClC,CAAC;IAED;;;;;OAKG;IACK,8CAAgB,GAAxB,UAAyB,QAA8B;QACtD,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;QAClE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;IAC/D,CAAC;IAED;;;;OAIG;IACI,0CAAY,GAAnB;QACC,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,CAAC,CAAC,EAAE;YACnC,OAAO,IAAI,CAAC,SAAS,CAAC;SACtB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;QACvC,IAAI,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC;QACnD,IAAI,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YAC/B,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;SAC9B;QACD,OAAO,IAAI,aAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IACpC,CAAC;IAED;;;;;;OAMG;IACI,uCAAS,GAAhB,UAAiB,KAAY;QAC5B,OAAO,CAAC,IAAI,CAAC,2GAA2G,CAAC,CAAC;QAC1H,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,YAAY,WAAW,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,YAAY,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,YAAY,UAAU,EAAE;YAC7H,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;SACnE;QACD,MAAM,IAAI,mCAAgB,CAAC,kKAAkK,CAAC,CAAC;IAChM,CAAC;IAED;;;;;OAKG;IACU,oCAAM,GAAnB;;;;;;wBACO,mBAAmB,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC;;;6BACtE,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW;wBAC/B,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;wBACtC,IAAI,SAAS,CAAC,QAAQ,KAAK,CAAC,CAAC,EAAE;4BACxB,GAAG,GAAG,IAAI,KAAK,CAAC,oGAAoG,CAAC,CAAC;4BAC5H,GAAG,CAAC,IAAI,GAAG,iBAAiB,CAAC;4BAC7B,MAAM,GAAG,CAAC;yBACV;wBACiB,qBAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAA;;wBAAhD,SAAS,GAAG,SAAoC;wBAClC,qBAAM,IAAI,CAAC,yBAAyB,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAA;;wBAAxF,WAAW,GAAG,SAA0E;wBAC9F,IAAI,CAAC,WAAW,EAAE;4BACjB,MAAM,IAAI,mCAAgB,CAAC,iEAAiE,CAAC,CAAC;yBAC9F;wBAEoB,qBAAM,2CAAoB,CAAC,WAAW,CAAC,WAAW,CAAC,EAAA;;wBAAlE,YAAY,GAAG,SAAmD;wBACxE;;;2BAGG;wBACH,IAAI,WAAW,CAAC,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,GAAG,IAAI,YAAY,CAAC,EAAE,CAAC,EAAE;4BAC5E,YAAY,GAAG,2BAAY,CAAC,kBAAkB,CAAC,YAAY,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC;4BACxF,sBAAO,YAAY,EAAC;yBACpB;wBAKK,GAAG,GAAyB;4BACjC,kBAAkB,EAAE,YAAY,CAAC,kBAAkB,IAAI,YAAY,CAAC,kBAAkB;4BACtF,kBAAkB,EAAE,YAAY,CAAC,kBAAkB,IAAI,YAAY,CAAC,kBAAkB;yBACtF,CAAC;wBACF,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;wBAC3B,IAAI,mBAAmB,IAAI,mBAAmB,CAAC,QAAQ,EAAE;4BACxD,mBAAmB,CAAC,QAAQ,CAAC,SAAS,EAAE,mBAAmB,CAAC,kBAAkB,CAAC,CAAC;yBAChF;;;;;;KAEF;IAED;;;;;;;;OAQG;IACU,yCAAW,GAAxB,UAAyB,SAAoC,EAAE,KAAY,EAAE,SAAiB;;;;4BACtF,qBAAM,IAAI,CAAC,MAAM;6BACtB,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;6BAC3B,OAAO,CAAC;4BACR,gBAAgB,EAAE,UAAG,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAE;4BAC1D,eAAe,EAAE,gBAAS,KAAK,CAAC,QAAQ,cAAI,KAAK,CAAC,QAAQ,cAAI,SAAS,CAAE;4BACzE,cAAc,EAAE,0BAA0B;yBAC1C,CAAC;6BACD,GAAG,CAAC,SAAS,CAAC,EAAA;4BAPhB,sBAAO,SAOS,EAAC;;;;KACjB;IAED;;;;;;;;OAQG;IACU,uDAAyB,GAAtC,UAAuC,SAAkB,EAAE,KAAY,EAAE,SAAiB;;;;4BAClF,qBAAM,IAAI,CAAC,MAAM;6BACtB,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;6BAC3B,OAAO,CAAC;4BACR,gBAAgB,EAAE,UAAG,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAE;4BAC1D,eAAe,EAAE,gBAAS,KAAK,CAAC,QAAQ,cAAI,KAAK,CAAC,QAAQ,cAAI,SAAS,CAAE;4BACzE,cAAc,EAAE,0BAA0B;yBAC1C,CAAC;6BACD,YAAY,CAAC,2BAAY,CAAC,GAAG,CAAC;6BAC9B,GAAG,CAAC,SAAS,CAAC,EAAA;4BARhB,sBAAO,SAQS,EAAC;;;;KACjB;IAED;;;;;OAKG;IACU,oCAAM,GAAnB;;;;;4BACwB,qBAAM,IAAI,CAAC,MAAM;6BACtC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;6BAC3B,YAAY,CAAC,2BAAY,CAAC,GAAG,CAAC;6BAC9B,MAAM,EAAE,EAAA;;wBAHJ,cAAc,GAAG,SAGb;wBACV,IAAI,cAAc,CAAC,MAAM,KAAK,GAAG,EAAE;4BAClC,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,IAAI,CAAC;yBACtC;wBACD,sBAAO,cAAc,EAAC;;;;KACtB;IAED;;;;;OAKG;IACU,uCAAS,GAAtB;;;;;4BACkB,qBAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAA;;wBAA9D,QAAQ,GAAG,SAAmD;wBACpE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;wBAChC,sBAAO,QAAQ,EAAC;;;;KAChB;IAED;;;;;OAKG;IACU,oCAAM,GAAnB;;;;4BACC,qBAAM,IAAI,CAAC,SAAS,EAAE,EAAA;;wBAAtB,SAAsB,CAAC;wBAChB,qBAAM,IAAI,CAAC,MAAM,EAAE,EAAA;4BAA1B,sBAAO,SAAmB,EAAC;;;;KAC3B;IAED;;;;;OAKG;IACI,8CAAgB,GAAvB;QACC,OAAO,IAAI,CAAC,aAAa,CAAC;IAC3B,CAAC;IACF,0BAAC;AAAD,CAAC,AA/RD,IA+RC;AA/RY,kDAAmB"}