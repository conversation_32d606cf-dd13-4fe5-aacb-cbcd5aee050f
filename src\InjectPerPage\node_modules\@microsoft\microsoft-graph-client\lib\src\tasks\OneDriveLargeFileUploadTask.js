"use strict";
/**
 * -------------------------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation.  All Rights Reserved.  Licensed under the MIT License.
 * See License in the project root for license information.
 * -------------------------------------------------------------------------------------------
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.OneDriveLargeFileUploadTask = void 0;
var tslib_1 = require("tslib");
/**
 * @module OneDriveLargeFileUploadTask
 */
var GraphClientError_1 = require("../GraphClientError");
var FileUpload_1 = require("./FileUploadTask/FileObjectClasses/FileUpload");
var LargeFileUploadTask_1 = require("./LargeFileUploadTask");
var OneDriveLargeFileUploadTaskUtil_1 = require("./OneDriveLargeFileUploadTaskUtil");
/**
 * @class
 * Class representing OneDriveLargeFileUploadTask
 */
var OneDriveLargeFileUploadTask = /** @class */ (function (_super) {
    (0, tslib_1.__extends)(OneDriveLargeFileUploadTask, _super);
    /**
     * @public
     * @constructor
     * Constructs a OneDriveLargeFileUploadTask
     * @param {Client} client - The GraphClient instance
     * @param {FileObject} file - The FileObject holding details of a file that needs to be uploaded
     * @param {LargeFileUploadSession} uploadSession - The upload session to which the upload has to be done
     * @param {LargeFileUploadTaskOptions} options - The upload task options
     * @returns An instance of OneDriveLargeFileUploadTask
     */
    function OneDriveLargeFileUploadTask(client, file, uploadSession, options) {
        return _super.call(this, client, file, uploadSession, options) || this;
    }
    /**
     * @private
     * @static
     * Constructs the create session url for Onedrive
     * @param {string} fileName - The name of the file
     * @param {path} [path = OneDriveLargeFileUploadTask.DEFAULT_UPLOAD_PATH] - The path for the upload
     * @returns The constructed create session url
     */
    OneDriveLargeFileUploadTask.constructCreateSessionUrl = function (fileName, path) {
        if (path === void 0) { path = OneDriveLargeFileUploadTask.DEFAULT_UPLOAD_PATH; }
        fileName = fileName.trim();
        path = path.trim();
        if (path === "") {
            path = "/";
        }
        if (path[0] !== "/") {
            path = "/".concat(path);
        }
        if (path[path.length - 1] !== "/") {
            path = "".concat(path, "/");
        }
        // we choose to encode each component of the file path separately because when encoding full URI
        // with encodeURI, special characters like # or % in the file name doesn't get encoded as desired
        return "/me/drive/root:".concat(path
            .split("/")
            .map(function (p) { return encodeURIComponent(p); })
            .join("/")).concat(encodeURIComponent(fileName), ":/createUploadSession");
    };
    /**
     * @private
     * @static
     * Get file information
     * @param {Blob | Uint8Array | File} file - The file entity
     * @param {string} fileName - The file name
     * @returns {FileInfo} The file information
     */
    OneDriveLargeFileUploadTask.getFileInfo = function (file, fileName) {
        var content;
        var size;
        if (typeof Blob !== "undefined" && file instanceof Blob) {
            content = new File([file], fileName);
            size = content.size;
        }
        else if (typeof File !== "undefined" && file instanceof File) {
            content = file;
            size = content.size;
        }
        else if (typeof Uint8Array !== "undefined" && file instanceof Uint8Array) {
            var b = file;
            size = b.byteLength;
            content = b.buffer.slice(b.byteOffset, b.byteOffset + b.byteLength);
        }
        return {
            content: content,
            size: size,
        };
    };
    /**
     * @public
     * @static
     * @async
     * Creates a OneDriveLargeFileUploadTask
     * @param {Client} client - The GraphClient instance
     * @param {Blob | Uint8Array | File} file - File represented as Blob, Uint8Array or File
     * @param {OneDriveLargeFileUploadOptions} options - The options for upload task
     * @returns The promise that will be resolves to OneDriveLargeFileUploadTask instance
     */
    OneDriveLargeFileUploadTask.create = function (client, file, options) {
        return (0, tslib_1.__awaiter)(this, void 0, void 0, function () {
            var fileName, fileInfo, fileObj;
            return (0, tslib_1.__generator)(this, function (_a) {
                if (!client || !file || !options) {
                    throw new GraphClientError_1.GraphClientError("Please provide the Graph client instance, file object and OneDriveLargeFileUploadOptions value");
                }
                fileName = options.fileName;
                fileInfo = OneDriveLargeFileUploadTask.getFileInfo(file, fileName);
                fileObj = new FileUpload_1.FileUpload(fileInfo.content, fileName, fileInfo.size);
                return [2 /*return*/, this.createTaskWithFileObject(client, fileObj, options)];
            });
        });
    };
    /**
     * @public
     * @static
     * @async
     * Creates a OneDriveLargeFileUploadTask
     * @param {Client} client - The GraphClient instance
     * @param {FileObject} fileObject - FileObject instance
     * @param {OneDriveLargeFileUploadOptions} options - The options for upload task
     * @returns The promise that will be resolves to OneDriveLargeFileUploadTask instance
     */
    OneDriveLargeFileUploadTask.createTaskWithFileObject = function (client, fileObject, options) {
        return (0, tslib_1.__awaiter)(this, void 0, void 0, function () {
            var requestUrl, uploadSessionPayload, session, rangeSize;
            return (0, tslib_1.__generator)(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!client || !fileObject || !options) {
                            throw new GraphClientError_1.GraphClientError("Please provide the Graph client instance, FileObject interface implementation and OneDriveLargeFileUploadOptions value");
                        }
                        requestUrl = OneDriveLargeFileUploadTask.constructCreateSessionUrl(options.fileName, options.path);
                        uploadSessionPayload = {
                            fileName: options.fileName,
                            fileDescription: options.fileDescription,
                            conflictBehavior: options.conflictBehavior,
                        };
                        return [4 /*yield*/, OneDriveLargeFileUploadTask.createUploadSession(client, requestUrl, uploadSessionPayload)];
                    case 1:
                        session = _a.sent();
                        rangeSize = (0, OneDriveLargeFileUploadTaskUtil_1.getValidRangeSize)(options.rangeSize);
                        return [2 /*return*/, new OneDriveLargeFileUploadTask(client, fileObject, session, {
                                rangeSize: rangeSize,
                                uploadEventHandlers: options.uploadEventHandlers,
                            })];
                }
            });
        });
    };
    /**
     * @public
     * @static
     * @async
     * Makes request to the server to create an upload session
     * @param {Client} client - The GraphClient instance
     * @param {string} requestUrl - The URL to create the upload session
     * @param {string} payloadOptions - The payload option. Default conflictBehavior is 'rename'
     * @returns The promise that resolves to LargeFileUploadSession
     */
    OneDriveLargeFileUploadTask.createUploadSession = function (client, requestUrl, payloadOptions) {
        return (0, tslib_1.__awaiter)(this, void 0, void 0, function () {
            var payload;
            return (0, tslib_1.__generator)(this, function (_a) {
                payload = {
                    item: {
                        "@microsoft.graph.conflictBehavior": (payloadOptions === null || payloadOptions === void 0 ? void 0 : payloadOptions.conflictBehavior) || "rename",
                        name: payloadOptions === null || payloadOptions === void 0 ? void 0 : payloadOptions.fileName,
                        description: payloadOptions === null || payloadOptions === void 0 ? void 0 : payloadOptions.fileDescription,
                    },
                };
                return [2 /*return*/, _super.createUploadSession.call(this, client, requestUrl, payload)];
            });
        });
    };
    /**
     * @public
     * Commits upload session to end uploading
     * @param {string} requestUrl - The URL to commit the upload session
     * @param {string} conflictBehavior - Conflict behaviour option. Default is 'rename'
     * @returns The promise resolves to committed response
     */
    OneDriveLargeFileUploadTask.prototype.commit = function (requestUrl, conflictBehavior) {
        if (conflictBehavior === void 0) { conflictBehavior = "rename"; }
        return (0, tslib_1.__awaiter)(this, void 0, void 0, function () {
            var payload;
            return (0, tslib_1.__generator)(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        payload = {
                            name: this.file.name,
                            "@microsoft.graph.conflictBehavior": conflictBehavior,
                            "@microsoft.graph.sourceUrl": this.uploadSession.url,
                        };
                        return [4 /*yield*/, this.client.api(requestUrl).put(payload)];
                    case 1: return [2 /*return*/, _a.sent()];
                }
            });
        });
    };
    /**
     * @private
     * @static
     * Default path for the file being uploaded
     */
    OneDriveLargeFileUploadTask.DEFAULT_UPLOAD_PATH = "/";
    return OneDriveLargeFileUploadTask;
}(LargeFileUploadTask_1.LargeFileUploadTask));
exports.OneDriveLargeFileUploadTask = OneDriveLargeFileUploadTask;
//# sourceMappingURL=OneDriveLargeFileUploadTask.js.map