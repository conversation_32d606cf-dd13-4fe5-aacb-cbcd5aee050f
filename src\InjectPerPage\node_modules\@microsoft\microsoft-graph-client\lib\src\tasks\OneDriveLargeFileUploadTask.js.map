{"version": 3, "file": "OneDriveLargeFileUploadTask.js", "sourceRoot": "", "sources": ["../../../src/tasks/OneDriveLargeFileUploadTask.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;AAEH;;GAEG;AAEH,wDAAuD;AAEvD,4EAA2E;AAE3E,6DAA4H;AAC5H,qFAAsE;AA6CtE;;;GAGG;AACH;IAAoD,4DAAsB;IAqIzE;;;;;;;;;OASG;IACH,qCAAmB,MAAc,EAAE,IAAmB,EAAE,aAAqC,EAAE,OAAmC;eACjI,kBAAM,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,CAAC;IAC5C,CAAC;IAzID;;;;;;;OAOG;IACY,qDAAyB,GAAxC,UAAyC,QAAgB,EAAE,IAA8D;QAA9D,qBAAA,EAAA,OAAe,2BAA2B,CAAC,mBAAmB;QACxH,QAAQ,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC3B,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QACnB,IAAI,IAAI,KAAK,EAAE,EAAE;YAChB,IAAI,GAAG,GAAG,CAAC;SACX;QACD,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACpB,IAAI,GAAG,WAAI,IAAI,CAAE,CAAC;SAClB;QACD,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;YAClC,IAAI,GAAG,UAAG,IAAI,MAAG,CAAC;SAClB;QACD,gGAAgG;QAChG,iGAAiG;QACjG,OAAO,yBAAkB,IAAI;aAC3B,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,kBAAkB,CAAC,CAAC,CAAC,EAArB,CAAqB,CAAC;aACjC,IAAI,CAAC,GAAG,CAAC,SAAG,kBAAkB,CAAC,QAAQ,CAAC,0BAAuB,CAAC;IACnE,CAAC;IAED;;;;;;;OAOG;IACY,uCAAW,GAA1B,UAA2B,IAA8B,EAAE,QAAgB;QAC1E,IAAI,OAAO,CAAC;QACZ,IAAI,IAAI,CAAC;QACT,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,IAAI,YAAY,IAAI,EAAE;YACxD,OAAO,GAAG,IAAI,IAAI,CAAC,CAAC,IAAY,CAAC,EAAE,QAAQ,CAAC,CAAC;YAC7C,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;SACpB;aAAM,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,IAAI,YAAY,IAAI,EAAE;YAC/D,OAAO,GAAG,IAAY,CAAC;YACvB,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;SACpB;aAAM,IAAI,OAAO,UAAU,KAAK,WAAW,IAAI,IAAI,YAAY,UAAU,EAAE;YAC3E,IAAM,CAAC,GAAG,IAAkB,CAAC;YAC7B,IAAI,GAAG,CAAC,CAAC,UAAU,CAAC;YACpB,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;SACpE;QACD,OAAO;YACN,OAAO,SAAA;YACP,IAAI,MAAA;SACJ,CAAC;IACH,CAAC;IAED;;;;;;;;;OASG;IACiB,kCAAM,GAA1B,UAA2B,MAAc,EAAE,IAA8B,EAAE,OAAuC;;;;gBACjH,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE;oBACjC,MAAM,IAAI,mCAAgB,CAAC,gGAAgG,CAAC,CAAC;iBAC7H;gBACK,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;gBAC5B,QAAQ,GAAG,2BAA2B,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBACnE,OAAO,GAAG,IAAI,uBAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC1E,sBAAO,IAAI,CAAC,wBAAwB,CAAkC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,EAAC;;;KAChG;IAED;;;;;;;;;OASG;IACiB,oDAAwB,GAA5C,UAAgD,MAAc,EAAE,UAAyB,EAAE,OAAuC;;;;;;wBACjI,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE;4BACvC,MAAM,IAAI,mCAAgB,CAAC,wHAAwH,CAAC,CAAC;yBACrJ;wBACK,UAAU,GAAG,2BAA2B,CAAC,yBAAyB,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;wBACnG,oBAAoB,GAAqC;4BAC9D,QAAQ,EAAE,OAAO,CAAC,QAAQ;4BAC1B,eAAe,EAAE,OAAO,CAAC,eAAe;4BACxC,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;yBAC1C,CAAC;wBACc,qBAAM,2BAA2B,CAAC,mBAAmB,CAAC,MAAM,EAAE,UAAU,EAAE,oBAAoB,CAAC,EAAA;;wBAAzG,OAAO,GAAG,SAA+F;wBACzG,SAAS,GAAG,IAAA,mDAAiB,EAAC,OAAO,CAAC,SAAS,CAAC,CAAC;wBACvD,sBAAO,IAAI,2BAA2B,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE;gCACnE,SAAS,WAAA;gCACT,mBAAmB,EAAE,OAAO,CAAC,mBAAmB;6BAChD,CAAC,EAAC;;;;KACH;IAED;;;;;;;;;OASG;IACiB,+CAAmB,GAAvC,UAAwC,MAAc,EAAE,UAAkB,EAAE,cAAgD;;;;gBACrH,OAAO,GAAG;oBACf,IAAI,EAAE;wBACL,mCAAmC,EAAE,CAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,gBAAgB,KAAI,QAAQ;wBACjF,IAAI,EAAE,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,QAAQ;wBAC9B,WAAW,EAAE,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,eAAe;qBAC5C;iBACD,CAAC;gBACF,sBAAO,OAAM,mBAAmB,YAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC,EAAC;;;KAC9D;IAgBD;;;;;;OAMG;IACU,4CAAM,GAAnB,UAAoB,UAAkB,EAAE,gBAA2B;QAA3B,iCAAA,EAAA,2BAA2B;;;;;;wBAC5D,OAAO,GAAG;4BACf,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI;4BACpB,mCAAmC,EAAE,gBAAgB;4BACrD,4BAA4B,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG;yBACpD,CAAC;wBACK,qBAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,EAAA;4BAArD,sBAAO,SAA8C,EAAC;;;;KACtD;IAhKD;;;;OAIG;IACY,+CAAmB,GAAG,GAAG,CAAC;IA4J1C,kCAAC;CAAA,AAlKD,CAAoD,yCAAmB,GAkKtE;AAlKY,kEAA2B"}