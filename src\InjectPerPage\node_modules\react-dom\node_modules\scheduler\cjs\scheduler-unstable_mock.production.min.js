/** @license React v0.20.2
 * scheduler-unstable_mock.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var f=0,g=null,h=null,k=-1,l=null,m=-1,n=!1,p=!1,q=!1,r=!1;function t(){return-1!==m&&null!==l&&l.length>=m||r&&q?n=!0:!1}function x(){if(p)throw Error("Already flushing work.");if(null!==g){var a=g;p=!0;try{var b=!0;do b=a(!0,f);while(b);b||(g=null);return!0}finally{p=!1}}else return!1}function z(a,b){var c=a.length;a.push(b);a:for(;;){var d=c-1>>>1,e=a[d];if(void 0!==e&&0<A(e,b))a[d]=b,a[c]=e,c=d;else break a}}function B(a){a=a[0];return void 0===a?null:a}
function C(a){var b=a[0];if(void 0!==b){var c=a.pop();if(c!==b){a[0]=c;a:for(var d=0,e=a.length;d<e;){var u=2*(d+1)-1,v=a[u],w=u+1,y=a[w];if(void 0!==v&&0>A(v,c))void 0!==y&&0>A(y,v)?(a[d]=y,a[w]=c,d=w):(a[d]=v,a[u]=c,d=u);else if(void 0!==y&&0>A(y,c))a[d]=y,a[w]=c,d=w;else break a}}return b}return null}function A(a,b){var c=a.sortIndex-b.sortIndex;return 0!==c?c:a.id-b.id}var D=[],E=[],F=1,G=null,H=3,I=!1,J=!1,K=!1;
function L(a){for(var b=B(E);null!==b;){if(null===b.callback)C(E);else if(b.startTime<=a)C(E),b.sortIndex=b.expirationTime,z(D,b);else break;b=B(E)}}function M(a){K=!1;L(a);if(!J)if(null!==B(D))J=!0,g=N;else{var b=B(E);null!==b&&(a=b.startTime-a,h=M,k=f+a)}}
function N(a,b){J=!1;K&&(K=!1,h=null,k=-1);I=!0;var c=H;try{L(b);for(G=B(D);null!==G&&(!(G.expirationTime>b)||a&&!t());){var d=G.callback;if("function"===typeof d){G.callback=null;H=G.priorityLevel;var e=d(G.expirationTime<=b);b=f;"function"===typeof e?G.callback=e:G===B(D)&&C(D);L(b)}else C(D);G=B(D)}if(null!==G)var u=!0;else{var v=B(E);if(null!==v){var w=v.startTime-b;h=M;k=f+w}u=!1}return u}finally{G=null,H=c,I=!1}}exports.unstable_IdlePriority=5;exports.unstable_ImmediatePriority=1;
exports.unstable_LowPriority=4;exports.unstable_NormalPriority=3;exports.unstable_Profiling=null;exports.unstable_UserBlockingPriority=2;exports.unstable_advanceTime=function(a){"disabledLog"!==console.log.name&&(f+=a,null!==h&&k<=f&&(h(f),k=-1,h=null))};exports.unstable_cancelCallback=function(a){a.callback=null};exports.unstable_clearYields=function(){if(null===l)return[];var a=l;l=null;return a};exports.unstable_continueExecution=function(){J||I||(J=!0,g=N)};
exports.unstable_flushAll=function(){if(null!==l)throw Error("Log is not empty. Assert on the log of yielded values before flushing additional work.");x();if(null!==l)throw Error("While flushing work, something yielded a value. Use an assertion helper to assert on the log of yielded values, e.g. expect(Scheduler).toFlushAndYield([...])");};exports.unstable_flushAllWithoutAsserting=x;
exports.unstable_flushExpired=function(){if(p)throw Error("Already flushing work.");if(null!==g){p=!0;try{g(!1,f)||(g=null)}finally{p=!1}}};exports.unstable_flushNumberOfYields=function(a){if(p)throw Error("Already flushing work.");if(null!==g){var b=g;m=a;p=!0;try{a=!0;do a=b(!0,f);while(a&&!n);a||(g=null)}finally{m=-1,p=n=!1}}};
exports.unstable_flushUntilNextPaint=function(){if(p)throw Error("Already flushing work.");if(null!==g){var a=g;r=!0;q=!1;p=!0;try{var b=!0;do b=a(!0,f);while(b&&!n);b||(g=null)}finally{p=n=r=!1}}};exports.unstable_forceFrameRate=function(){};exports.unstable_getCurrentPriorityLevel=function(){return H};exports.unstable_getFirstCallbackNode=function(){return B(D)};exports.unstable_next=function(a){switch(H){case 1:case 2:case 3:var b=3;break;default:b=H}var c=H;H=b;try{return a()}finally{H=c}};
exports.unstable_now=function(){return f};exports.unstable_pauseExecution=function(){};exports.unstable_requestPaint=function(){q=!0};exports.unstable_runWithPriority=function(a,b){switch(a){case 1:case 2:case 3:case 4:case 5:break;default:a=3}var c=H;H=a;try{return b()}finally{H=c}};
exports.unstable_scheduleCallback=function(a,b,c){var d=f;"object"===typeof c&&null!==c?(c=c.delay,c="number"===typeof c&&0<c?d+c:d):c=d;switch(a){case 1:var e=-1;break;case 2:e=250;break;case 5:e=1073741823;break;case 4:e=1E4;break;default:e=5E3}e=c+e;a={id:F++,callback:b,priorityLevel:a,startTime:c,expirationTime:e,sortIndex:-1};c>d?(a.sortIndex=c,z(E,a),null===B(D)&&a===B(E)&&(K?(h=null,k=-1):K=!0,h=M,k=f+(c-d))):(a.sortIndex=e,z(D,a),J||I||(J=!0,g=N));return a};exports.unstable_shouldYield=t;
exports.unstable_wrapCallback=function(a){var b=H;return function(){var c=H;H=b;try{return a.apply(this,arguments)}finally{H=c}}};exports.unstable_yieldValue=function(a){"disabledLog"!==console.log.name&&(null===l?l=[a]:l.push(a))};
