/** @license React v17.0.2
 * react-dom-server.browser.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
(function(){'use strict';(function(x,r){"object"===typeof exports&&"undefined"!==typeof module?r(exports,require("react")):"function"===typeof define&&define.amd?define(["exports","react"],r):(x=x||self,r(x.ReactDOMServer={},x.React))})(this,function(x,r){function t(a){for(var b="https://reactjs.org/docs/error-decoder.html?invariant="+a,c=1;c<arguments.length;c++)b+="&args[]="+encodeURIComponent(arguments[c]);return"Minified React error #"+a+"; visit "+b+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}
function E(a){if(null==a)return null;if("function"===typeof a)return a.displayName||a.name||null;if("string"===typeof a)return a;switch(a){case I:return"Fragment";case P:return"Portal";case Q:return"Profiler";case R:return"StrictMode";case J:return"Suspense";case S:return"SuspenseList"}if("object"===typeof a)switch(a.$$typeof){case T:return(a.displayName||"Context")+".Consumer";case K:return(a._context.displayName||"Context")+".Provider";case U:var b=a.render;b=b.displayName||b.name||"";return a.displayName||
(""!==b?"ForwardRef("+b+")":"ForwardRef");case V:return E(a.type);case ea:return E(a._render);case W:b=a._payload;a=a._init;try{return E(a(b))}catch(c){}}return null}function F(a,b){for(var c=a._threadCount|0;c<=b;c++)a[c]=a._currentValue2,a._threadCount=c+1}function Aa(a,b,c,d){if(d&&(d=a.contextType,"object"===typeof d&&null!==d))return F(d,c),d[c];if(a=a.contextTypes){c={};for(var f in a)c[f]=b[f];b=c}else b=fa;return b}function ha(a){if(ia.call(ja,a))return!0;if(ia.call(ka,a))return!1;if(Ba.test(a))return ja[a]=
!0;ka[a]=!0;return!1}function Ca(a,b,c,d){if(null!==c&&0===c.type)return!1;switch(typeof b){case "function":case "symbol":return!0;case "boolean":if(d)return!1;if(null!==c)return!c.acceptsBooleans;a=a.toLowerCase().slice(0,5);return"data-"!==a&&"aria-"!==a;default:return!1}}function Da(a,b,c,d){if(null===b||"undefined"===typeof b||Ca(a,b,c,d))return!0;if(d)return!1;if(null!==c)switch(c.type){case 3:return!b;case 4:return!1===b;case 5:return isNaN(b);case 6:return isNaN(b)||1>b}return!1}function q(a,
b,c,d,f,k,w){this.acceptsBooleans=2===b||3===b||4===b;this.attributeName=d;this.attributeNamespace=f;this.mustUseProperty=c;this.propertyName=a;this.type=b;this.sanitizeURL=k;this.removeEmptyString=w}function C(a){if("boolean"===typeof a||"number"===typeof a)return""+a;a=""+a;var b=Ea.exec(a);if(b){var c="",d,f=0;for(d=b.index;d<a.length;d++){switch(a.charCodeAt(d)){case 34:b="&quot;";break;case 38:b="&amp;";break;case 39:b="&#x27;";break;case 60:b="&lt;";break;case 62:b="&gt;";break;default:continue}f!==
d&&(c+=a.substring(f,d));f=d+1;c+=b}a=f!==d?c+a.substring(f,d):c}return a}function Fa(a,b){var c=m.hasOwnProperty(a)?m[a]:null;var d;if(d="style"!==a)d=null!==c?0===c.type:!(2<a.length)||"o"!==a[0]&&"O"!==a[0]||"n"!==a[1]&&"N"!==a[1]?!1:!0;if(d||Da(a,b,c,!1))return"";if(null!==c){a=c.attributeName;d=c.type;if(3===d||4===d&&!0===b)return a+'=""';c.sanitizeURL&&(b=""+b);return a+'="'+(C(b)+'"')}return ha(a)?a+'="'+(C(b)+'"'):""}function Ga(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}function A(){if(null===
u)throw Error(t(321));return u}function la(){if(0<L)throw Error(t(312));return{memoizedState:null,queue:null,next:null}}function X(){null===g?null===M?(G=!1,M=g=la()):(G=!0,g=M):null===g.next?(G=!1,g=g.next=la()):(G=!0,g=g.next);return g}function ma(a,b,c,d){for(;N;)N=!1,L+=1,g=null,c=a(b,d);na();return c}function na(){u=null;N=!1;M=null;L=0;g=v=null}function oa(a,b){return"function"===typeof b?b(a):b}function pa(a,b,c){u=A();g=X();if(G){var d=g.queue;b=d.dispatch;if(null!==v&&(c=v.get(d),void 0!==
c)){v.delete(d);d=g.memoizedState;do d=a(d,c.action),c=c.next;while(null!==c);g.memoizedState=d;return[d,b]}return[g.memoizedState,b]}a=a===oa?"function"===typeof b?b():b:void 0!==c?c(b):b;g.memoizedState=a;a=g.queue={last:null,dispatch:null};a=a.dispatch=Ha.bind(null,u,a);return[g.memoizedState,a]}function qa(a,b){u=A();g=X();b=void 0===b?null:b;if(null!==g){var c=g.memoizedState;if(null!==c&&null!==b){var d=c[1];a:if(null===d)d=!1;else{for(var f=0;f<d.length&&f<b.length;f++)if(!Ia(b[f],d[f])){d=
!1;break a}d=!0}if(d)return c[0]}}a=a();g.memoizedState=[a,b];return a}function Ha(a,b,c){if(!(25>L))throw Error(t(301));if(a===u)if(N=!0,a={action:c,next:null},null===v&&(v=new Map),c=v.get(b),void 0===c)v.set(b,a);else{for(b=c;null!==b.next;)b=b.next;b.next=a}}function Y(){}function ra(a){switch(a){case "svg":return"http://www.w3.org/2000/svg";case "math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Ja(a){if(void 0===a||null===a)return a;var b=
"";r.Children.forEach(a,function(a){null!=a&&(b+=a)});return b}function sa(a,b){if(void 0===a)throw Error(t(152,E(b)||"Component"));}function Ka(a,b,c){function d(d,k){var e=k.prototype&&k.prototype.isReactComponent,f=Aa(k,b,c,e),w=[],h=!1,g={isMounted:function(a){return!1},enqueueForceUpdate:function(a){if(null===w)return null},enqueueReplaceState:function(a,c){h=!0;w=[c]},enqueueSetState:function(a,c){if(null===w)return null;w.push(c)}};if(e){if(e=new k(d.props,f,g),"function"===typeof k.getDerivedStateFromProps){var p=
k.getDerivedStateFromProps.call(null,d.props,e.state);null!=p&&(e.state=z({},e.state,p))}}else if(u={},e=k(d.props,f,g),e=ma(k,d.props,e,f),null==e||null==e.render){a=e;sa(a,k);return}e.props=d.props;e.context=f;e.updater=g;g=e.state;void 0===g&&(e.state=g=null);if("function"===typeof e.UNSAFE_componentWillMount||"function"===typeof e.componentWillMount)if("function"===typeof e.componentWillMount&&"function"!==typeof k.getDerivedStateFromProps&&e.componentWillMount(),"function"===typeof e.UNSAFE_componentWillMount&&
"function"!==typeof k.getDerivedStateFromProps&&e.UNSAFE_componentWillMount(),w.length){g=w;var l=h;w=null;h=!1;if(l&&1===g.length)e.state=g[0];else{p=l?g[0]:e.state;var m=!0;for(l=l?1:0;l<g.length;l++){var n=g[l];n="function"===typeof n?n.call(e,p,d.props,f):n;null!=n&&(m?(m=!1,p=z({},p,n)):z(p,n))}e.state=p}}else w=null;a=e.render();sa(a,k);if("function"===typeof e.getChildContext&&(d=k.childContextTypes,"object"===typeof d)){var q=e.getChildContext();for(var r in q)if(!(r in d))throw Error(t(108,
E(k)||"Unknown",r));}q&&(b=z({},b,q))}for(;r.isValidElement(a);){var f=a,k=f.type;if("function"!==typeof k)break;d(f,k)}return{child:a,context:b}}var z=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.assign,P=60106,I=60107,R=60108,Q=60114,K=60109,T=60110,U=60112,J=60113,S=60120,V=60115,W=60116,ea=60121,ta=60117,ua=60119,va=60129,wa=60131;if("function"===typeof Symbol&&Symbol.for){var l=Symbol.for;P=l("react.portal");I=l("react.fragment");R=l("react.strict_mode");Q=l("react.profiler");K=l("react.provider");
T=l("react.context");U=l("react.forward_ref");J=l("react.suspense");S=l("react.suspense_list");V=l("react.memo");W=l("react.lazy");ea=l("react.block");ta=l("react.fundamental");ua=l("react.scope");va=l("react.debug_trace_mode");wa=l("react.legacy_hidden")}l=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;for(var fa={},n=new Uint16Array(16),O=0;15>O;O++)n[O]=O+1;n[15]=0;var Ba=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,
ia=Object.prototype.hasOwnProperty,ka={},ja={},m={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(a){m[a]=new q(a,0,!1,a,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(a){var b=a[0];m[b]=new q(b,1,!1,a[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(a){m[a]=
new q(a,2,!1,a.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(a){m[a]=new q(a,2,!1,a,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(a){m[a]=new q(a,3,!1,a.toLowerCase(),null,!1,!1)});["checked","multiple",
"muted","selected"].forEach(function(a){m[a]=new q(a,3,!0,a,null,!1,!1)});["capture","download"].forEach(function(a){m[a]=new q(a,4,!1,a,null,!1,!1)});["cols","rows","size","span"].forEach(function(a){m[a]=new q(a,6,!1,a,null,!1,!1)});["rowSpan","start"].forEach(function(a){m[a]=new q(a,5,!1,a.toLowerCase(),null,!1,!1)});var aa=/[\-:]([a-z])/g,ba=function(a){return a[1].toUpperCase()};"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(a){var b=
a.replace(aa,ba);m[b]=new q(b,1,!1,a,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(a){var b=a.replace(aa,ba);m[b]=new q(b,1,!1,a,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(a){var b=a.replace(aa,ba);m[b]=new q(b,1,!1,a,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(a){m[a]=new q(a,1,!1,a.toLowerCase(),null,!1,!1)});m.xlinkHref=new q("xlinkHref",
1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(a){m[a]=new q(a,1,!1,a.toLowerCase(),null,!0,!0)});var Ea=/["'&<>]/,Ia="function"===typeof Object.is?Object.is:Ga,u=null,M=null,g=null,G=!1,N=!1,v=null,L=0,D=null,La={readContext:function(a,b){b=D.threadID;F(a,b);return a[b]},useContext:function(a,b){A();b=D.threadID;F(a,b);return a[b]},useMemo:qa,useReducer:pa,useRef:function(a){u=A();g=X();var b=g.memoizedState;return null===b?(a={current:a},
g.memoizedState=a):b},useState:function(a){return pa(oa,a)},useLayoutEffect:function(a,b){},useCallback:function(a,b){return qa(function(){return a},b)},useImperativeHandle:Y,useEffect:Y,useDebugValue:Y,useDeferredValue:function(a){A();return a},useTransition:function(){A();return[function(a){a()},!1]},useOpaqueIdentifier:function(){return(D.identifierPrefix||"")+"R:"+(D.uniqueID++).toString(36)},useMutableSource:function(a,b,c){A();return b(a._source)}},xa={area:!0,base:!0,br:!0,col:!0,embed:!0,
hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0},Ma=z({menuitem:!0},xa),H={animationIterationCount:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,
lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Na=["Webkit","ms","Moz","O"];Object.keys(H).forEach(function(a){Na.forEach(function(b){b=b+a.charAt(0).toUpperCase()+a.substring(1);H[b]=H[a]})});var Oa=/([A-Z])/g,Pa=/^ms-/,B=r.Children.toArray,ca=l.ReactCurrentDispatcher,Qa={listing:!0,pre:!0,textarea:!0},Ra=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,
ya={},da={},Sa=Object.prototype.hasOwnProperty,Ta={children:null,dangerouslySetInnerHTML:null,suppressContentEditableWarning:null,suppressHydrationWarning:null},za=function(){function a(a,b,f){r.isValidElement(a)?a.type!==I?a=[a]:(a=a.props.children,a=r.isValidElement(a)?[a]:B(a)):a=B(a);a={type:null,domNamespace:"http://www.w3.org/1999/xhtml",children:a,childIndex:0,context:fa,footer:""};var c=n[0];if(0===c){var d=n;c=d.length;var h=2*c;if(!(65536>=h))throw Error(t(304));var e=new Uint16Array(h);
e.set(d);n=e;n[0]=c+1;for(d=c;d<h-1;d++)n[d]=d+1;n[h-1]=0}else n[0]=n[c];this.threadID=c;this.stack=[a];this.exhausted=!1;this.currentSelectValue=null;this.previousWasTextNode=!1;this.makeStaticMarkup=b;this.suspenseDepth=0;this.contextIndex=-1;this.contextStack=[];this.contextValueStack=[];this.uniqueID=0;this.identifierPrefix=f&&f.identifierPrefix||""}var b=a.prototype;b.destroy=function(){if(!this.exhausted){this.exhausted=!0;this.clearProviders();var a=this.threadID;n[a]=n[0];n[0]=a}};b.pushProvider=
function(a){var b=++this.contextIndex,c=a.type._context,k=this.threadID;F(c,k);var w=c[k];this.contextStack[b]=c;this.contextValueStack[b]=w;c[k]=a.props.value};b.popProvider=function(a){a=this.contextIndex;var b=this.contextStack[a],c=this.contextValueStack[a];this.contextStack[a]=null;this.contextValueStack[a]=null;this.contextIndex--;b[this.threadID]=c};b.clearProviders=function(){for(var a=this.contextIndex;0<=a;a--)this.contextStack[a][this.threadID]=this.contextValueStack[a]};b.read=function(a){if(this.exhausted)return null;
var b=D;D=this;var c=ca.current;ca.current=La;try{for(var k=[""],w=!1;k[0].length<a;){if(0===this.stack.length){this.exhausted=!0;var h=this.threadID;n[h]=n[0];n[0]=h;break}var e=this.stack[this.stack.length-1];if(w||e.childIndex>=e.children.length){var g=e.footer;""!==g&&(this.previousWasTextNode=!1);this.stack.pop();if("select"===e.type)this.currentSelectValue=null;else if(null!=e.type&&null!=e.type.type&&e.type.type.$$typeof===K)this.popProvider(e.type);else if(e.type===J){this.suspenseDepth--;
var l=k.pop();if(w){w=!1;var m=e.fallbackFrame;if(!m)throw Error(t(303));this.stack.push(m);k[this.suspenseDepth]+="\x3c!--$!--\x3e";continue}else k[this.suspenseDepth]+=l}k[this.suspenseDepth]+=g}else{var q=e.children[e.childIndex++],p="";try{p+=this.render(q,e.context,e.domNamespace)}catch(Z){if(null!=Z&&"function"===typeof Z.then)throw Error(t(294));throw Z;}finally{}k.length<=this.suspenseDepth&&k.push("");k[this.suspenseDepth]+=p}}return k[0]}finally{ca.current=c,D=b,na()}};b.render=function(a,
b,f){if("string"===typeof a||"number"===typeof a){f=""+a;if(""===f)return"";if(this.makeStaticMarkup)return C(f);if(this.previousWasTextNode)return"\x3c!-- --\x3e"+C(f);this.previousWasTextNode=!0;return C(f)}b=Ka(a,b,this.threadID);a=b.child;b=b.context;if(null===a||!1===a)return"";if(!r.isValidElement(a)){if(null!=a&&null!=a.$$typeof){f=a.$$typeof;if(f===P)throw Error(t(257));throw Error(t(258,f.toString()));}a=B(a);this.stack.push({type:null,domNamespace:f,children:a,childIndex:0,context:b,footer:""});
return""}var c=a.type;if("string"===typeof c)return this.renderDOM(a,b,f);switch(c){case wa:case va:case R:case Q:case S:case I:return a=B(a.props.children),this.stack.push({type:null,domNamespace:f,children:a,childIndex:0,context:b,footer:""}),"";case J:throw Error(t(294));case ua:throw Error(t(343));}if("object"===typeof c&&null!==c)switch(c.$$typeof){case U:u={};var d=c.render(a.props,a.ref);d=ma(c.render,a.props,d,a.ref);d=B(d);this.stack.push({type:null,domNamespace:f,children:d,childIndex:0,
context:b,footer:""});return"";case V:return a=[r.createElement(c.type,z({ref:a.ref},a.props))],this.stack.push({type:null,domNamespace:f,children:a,childIndex:0,context:b,footer:""}),"";case K:return c=B(a.props.children),f={type:a,domNamespace:f,children:c,childIndex:0,context:b,footer:""},this.pushProvider(a),this.stack.push(f),"";case T:c=a.type;d=a.props;var h=this.threadID;F(c,h);c=B(d.children(c[h]));this.stack.push({type:a,domNamespace:f,children:c,childIndex:0,context:b,footer:""});return"";
case ta:throw Error(t(338));case W:return c=a.type,d=c._init,c=d(c._payload),a=[r.createElement(c,z({ref:a.ref},a.props))],this.stack.push({type:null,domNamespace:f,children:a,childIndex:0,context:b,footer:""}),""}throw Error(t(130,null==c?c:typeof c,""));};b.renderDOM=function(a,b,f){var c=a.type.toLowerCase();"http://www.w3.org/1999/xhtml"===f&&ra(c);if(!ya.hasOwnProperty(c)){if(!Ra.test(c))throw Error(t(65,c));ya[c]=!0}var d=a.props;if("input"===c)d=z({type:void 0},d,{defaultChecked:void 0,defaultValue:void 0,
value:null!=d.value?d.value:d.defaultValue,checked:null!=d.checked?d.checked:d.defaultChecked});else if("textarea"===c){var h=d.value;if(null==h){h=d.defaultValue;var e=d.children;if(null!=e){if(null!=h)throw Error(t(92));if(Array.isArray(e)){if(!(1>=e.length))throw Error(t(93));e=e[0]}h=""+e}null==h&&(h="")}d=z({},d,{value:void 0,children:""+h})}else if("select"===c)this.currentSelectValue=null!=d.value?d.value:d.defaultValue,d=z({},d,{value:void 0});else if("option"===c){e=this.currentSelectValue;
var g=Ja(d.children);if(null!=e){var l=null!=d.value?d.value+"":g;h=!1;if(Array.isArray(e))for(var n=0;n<e.length;n++){if(""+e[n]===l){h=!0;break}}else h=""+e===l;d=z({selected:void 0,children:void 0},d,{selected:h,children:g})}}if(h=d){if(Ma[c]&&(null!=h.children||null!=h.dangerouslySetInnerHTML))throw Error(t(137,c));if(null!=h.dangerouslySetInnerHTML){if(null!=h.children)throw Error(t(60));if(!("object"===typeof h.dangerouslySetInnerHTML&&"__html"in h.dangerouslySetInnerHTML))throw Error(t(61));
}if(null!=h.style&&"object"!==typeof h.style)throw Error(t(62));}h=d;e=this.makeStaticMarkup;g=1===this.stack.length;l="<"+a.type;b:if(-1===c.indexOf("-"))n="string"===typeof h.is;else switch(c){case "annotation-xml":case "color-profile":case "font-face":case "font-face-src":case "font-face-uri":case "font-face-format":case "font-face-name":case "missing-glyph":n=!1;break b;default:n=!0}for(y in h)if(Sa.call(h,y)){var m=h[y];if(null!=m){if("style"===y){var p=void 0,q="",r="";for(p in m)if(m.hasOwnProperty(p)){var x=
0===p.indexOf("--"),u=m[p];if(null!=u){if(x)var v=p;else if(v=p,da.hasOwnProperty(v))v=da[v];else{var A=v.replace(Oa,"-$1").toLowerCase().replace(Pa,"-ms-");v=da[v]=A}q+=r+v+":";r=p;x=null==u||"boolean"===typeof u||""===u?"":x||"number"!==typeof u||0===u||H.hasOwnProperty(r)&&H[r]?(""+u).trim():u+"px";q+=x;r=";"}}m=q||null}p=null;n?Ta.hasOwnProperty(y)||(p=y,p=ha(p)&&null!=m?p+'="'+(C(m)+'"'):""):p=Fa(y,m);p&&(l+=" "+p)}}e||g&&(l+=' data-reactroot=""');var y=l;h="";xa.hasOwnProperty(c)?y+="/>":(y+=
">",h="</"+a.type+">");a:{e=d.dangerouslySetInnerHTML;if(null!=e){if(null!=e.__html){e=e.__html;break a}}else if(e=d.children,"string"===typeof e||"number"===typeof e){e=C(e);break a}e=null}null!=e?(d=[],Qa.hasOwnProperty(c)&&"\n"===e.charAt(0)&&(y+="\n"),y+=e):d=B(d.children);a=a.type;f=null==f||"http://www.w3.org/1999/xhtml"===f?ra(a):"http://www.w3.org/2000/svg"===f&&"foreignObject"===a?"http://www.w3.org/1999/xhtml":f;this.stack.push({domNamespace:f,type:c,children:d,childIndex:0,context:b,footer:h});
this.previousWasTextNode=!1;return y};return a}();x.renderToNodeStream=function(){throw Error(t(207));};x.renderToStaticMarkup=function(a,b){a=new za(a,!0,b);try{return a.read(Infinity)}finally{a.destroy()}};x.renderToStaticNodeStream=function(){throw Error(t(208));};x.renderToString=function(a,b){a=new za(a,!1,b);try{return a.read(Infinity)}finally{a.destroy()}};x.version="17.0.2"});
})();
