/** @license React v17.0.2
 * react-dom-test-utils.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
(function(){'use strict';(function(h,p){"object"===typeof exports&&"undefined"!==typeof module?p(exports,require("react"),require("react-dom")):"function"===typeof define&&define.amd?define(["exports","react","react-dom"],p):(h=h||self,p(h.ReactTestUtils={},h.React,h.ReactDOM))})(this,function(h,p,v){function q(a){for(var b="https://reactjs.org/docs/error-decoder.html?invariant="+a,c=1;c<arguments.length;c++)b+="&args[]="+encodeURIComponent(arguments[c]);return"Minified React error #"+a+"; visit "+b+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}
function T(a){var b=a,c=a;if(a.alternate)for(;b.return;)b=b.return;else{a=b;do b=a,0!==(b.flags&1026)&&(c=b.return),a=b.return;while(a)}return 3===b.tag?c:null}function U(a){if(T(a)!==a)throw Error(q(188));}function ka(a){var b=a.alternate;if(!b){b=T(a);if(null===b)throw Error(q(188));return b!==a?null:a}for(var c=a,d=b;;){var e=c.return;if(null===e)break;var g=e.alternate;if(null===g){d=e.return;if(null!==d){c=d;continue}break}if(e.child===g.child){for(g=e.child;g;){if(g===c)return U(e),a;if(g===
d)return U(e),b;g=g.sibling}throw Error(q(188));}if(c.return!==d.return)c=e,d=g;else{for(var f=!1,l=e.child;l;){if(l===c){f=!0;c=e;d=g;break}if(l===d){f=!0;d=e;c=g;break}l=l.sibling}if(!f){for(l=g.child;l;){if(l===c){f=!0;c=g;d=e;break}if(l===d){f=!0;d=g;c=e;break}l=l.sibling}if(!f)throw Error(q(189));}}if(c.alternate!==d)throw Error(q(190));}if(3!==c.tag)throw Error(q(188));return c.stateNode.current===c?a:b}function I(a){var b=a.keyCode;"charCode"in a?(a=a.charCode,0===a&&13===b&&(a=13)):a=b;10===
a&&(a=13);return 32<=a||13===a?a:0}function B(){return!0}function V(){return!1}function m(a){function b(c,b,e,g,f){this._reactName=c;this._targetInst=e;this.type=b;this.nativeEvent=g;this.target=f;this.currentTarget=null;for(var d in a)a.hasOwnProperty(d)&&(c=a[d],this[d]=c?c(g):g[d]);this.isDefaultPrevented=(null!=g.defaultPrevented?g.defaultPrevented:!1===g.returnValue)?B:V;this.isPropagationStopped=V;return this}k(b.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;
a&&(a.preventDefault?a.preventDefault():"unknown"!==typeof a.returnValue&&(a.returnValue=!1),this.isDefaultPrevented=B)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():"unknown"!==typeof a.cancelBubble&&(a.cancelBubble=!0),this.isPropagationStopped=B)},persist:function(){},isPersistent:B});return b}function la(a){var b=this.nativeEvent;return b.getModifierState?b.getModifierState(a):(a=ma[a])?!!b[a]:!1}function J(a){return la}function W(a){if(null===C)try{var b=
("require"+Math.random()).slice(0,7);C=(module&&module[b]).call(module,"timers").setImmediate}catch(c){C=function(a){var b=new MessageChannel;b.port1.onmessage=a;b.port2.postMessage(void 0)}}return C(a)}function X(a){try{K(),W(function(){K()?X(a):a()})}catch(b){a(b)}}function Y(a,b){jest.runOnlyPendingTimers();W(function(){try{y()?Y(a,b):a()}catch(c){b(c)}})}function na(a,b,c,d,e,g,f,l,h){z=!1;D=null;oa.apply(pa,arguments)}function qa(a,b,c,d,e,g,f,l,h){na.apply(this,arguments);if(z){if(z){var k=
D;z=!1;D=null}else throw Error(q(198));E||(E=!0,L=k)}}function ra(a){}function sa(a,b){if(!a)return[];a=ka(a);if(!a)return[];for(var c=a,d=[];;){if(5===c.tag||6===c.tag||1===c.tag||0===c.tag){var e=c.stateNode;b(e)&&d.push(e)}if(c.child)c.child.return=c,c=c.child;else{if(c===a)return d;for(;!c.sibling;){if(!c.return||c.return===a)return d;c=c.return}c.sibling.return=c.return;c=c.sibling}}}function w(a,b){if(a&&!a._reactInternals){var c=""+a;a=Array.isArray(a)?"an array":a&&1===a.nodeType&&a.tagName?
"a DOM node":"[object Object]"===c?"object with keys {"+Object.keys(a).join(", ")+"}":c;throw Error(q(286,b,a));}}function F(a){return!(!a||1!==a.nodeType||!a.tagName)}function M(a){return F(a)?!1:null!=a&&"function"===typeof a.render&&"function"===typeof a.setState}function Z(a,b){return M(a)?a._reactInternals.type===b:!1}function G(a,b){w(a,"findAllInRenderedTree");return a?sa(a._reactInternals,b):[]}function aa(a,b){w(a,"scryRenderedDOMComponentsWithClass");return G(a,function(a){if(F(a)){var c=
a.className;"string"!==typeof c&&(c=a.getAttribute("class")||"");var e=c.split(/\s+/);if(!Array.isArray(b)){if(void 0===b)throw Error(q(11));b=b.split(/\s+/)}return b.every(function(a){return-1!==e.indexOf(a)})}return!1})}function ba(a,b){w(a,"scryRenderedDOMComponentsWithTag");return G(a,function(a){return F(a)&&a.tagName.toUpperCase()===b.toUpperCase()})}function ca(a,b){w(a,"scryRenderedComponentsWithType");return G(a,function(a){return Z(a,b)})}function da(a,b,c){var d=a.type||"unknown-event";
a.currentTarget=ta(c);qa(d,b,void 0,a);a.currentTarget=null}function ea(a,b,c){for(var d=[];a;){d.push(a);do a=a.return;while(a&&5!==a.tag);a=a?a:null}for(a=d.length;0<a--;)b(d[a],"captured",c);for(a=0;a<d.length;a++)b(d[a],"bubbled",c)}function fa(a,b){var c=a.stateNode;if(!c)return null;var d=ua(c);if(!d)return null;c=d[b];a:switch(b){case "onClick":case "onClickCapture":case "onDoubleClick":case "onDoubleClickCapture":case "onMouseDown":case "onMouseDownCapture":case "onMouseMove":case "onMouseMoveCapture":case "onMouseUp":case "onMouseUpCapture":case "onMouseEnter":(d=
!d.disabled)||(a=a.type,d=!("button"===a||"input"===a||"select"===a||"textarea"===a));a=!d;break a;default:a=!1}if(a)return null;if(c&&"function"!==typeof c)throw Error(q(231,b,typeof c));return c}function va(a,b,c){a&&c&&c._reactName&&(b=fa(a,c._reactName))&&(null==c._dispatchListeners&&(c._dispatchListeners=[]),null==c._dispatchInstances&&(c._dispatchInstances=[]),c._dispatchListeners.push(b),c._dispatchInstances.push(a))}function wa(a,b,c){var d=c._reactName;"captured"===b&&(d+="Capture");if(b=
fa(a,d))null==c._dispatchListeners&&(c._dispatchListeners=[]),null==c._dispatchInstances&&(c._dispatchInstances=[]),c._dispatchListeners.push(b),c._dispatchInstances.push(a)}function xa(a){return function(b,c){if(p.isValidElement(b))throw Error(q(228));if(M(b))throw Error(q(229));var d="on"+a[0].toUpperCase()+a.slice(1),e=new ra;e.target=b;e.type=a.toLowerCase();var g=ya(b),f=new za(d,e.type,g,e,b);f.persist();k(f,c);Aa.has(a)?f&&f._reactName&&va(f._targetInst,null,f):f&&f._reactName&&ea(f._targetInst,
wa,f);v.unstable_batchedUpdates(function(){Ba(b);if(f){var a=f._dispatchListeners,c=f._dispatchInstances;if(Array.isArray(a))for(var d=0;d<a.length&&!f.isPropagationStopped();d++)da(f,a[d],c[d]);else a&&da(f,a,c);f._dispatchListeners=null;f._dispatchInstances=null;f.isPersistent()||f.constructor.release(f)}if(E)throw a=L,E=!1,L=null,a;});Ca()}}var k=p.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.assign,t=p.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,u={eventPhase:0,bubbles:0,cancelable:0,
timeStamp:function(a){return a.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},za=m(u),x=k({},u,{view:0,detail:0});m(x);var N,O,A,r=k({},x,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:J,button:0,buttons:0,relatedTarget:function(a){return void 0===a.relatedTarget?a.fromElement===a.srcElement?a.toElement:a.fromElement:a.relatedTarget},movementX:function(a){if("movementX"in a)return a.movementX;a!==A&&(A&&"mousemove"===a.type?
(N=a.screenX-A.screenX,O=a.screenY-A.screenY):O=N=0,A=a);return N},movementY:function(a){return"movementY"in a?a.movementY:O}});m(r);var n=k({},r,{dataTransfer:0});m(n);n=k({},x,{relatedTarget:0});m(n);n=k({},u,{animationName:0,elapsedTime:0,pseudoElement:0});m(n);n=k({},u,{clipboardData:function(a){return"clipboardData"in a?a.clipboardData:window.clipboardData}});m(n);n=k({},u,{data:0});m(n);var Da={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",
Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Ea={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},
ma={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};n=k({},x,{key:function(a){if(a.key){var b=Da[a.key]||a.key;if("Unidentified"!==b)return b}return"keypress"===a.type?(a=I(a),13===a?"Enter":String.fromCharCode(a)):"keydown"===a.type||"keyup"===a.type?Ea[a.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:J,charCode:function(a){return"keypress"===a.type?I(a):0},keyCode:function(a){return"keydown"===a.type||"keyup"===
a.type?a.keyCode:0},which:function(a){return"keypress"===a.type?I(a):"keydown"===a.type||"keyup"===a.type?a.keyCode:0}});m(n);n=k({},r,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0});m(n);x=k({},x,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:J});m(x);u=k({},u,{propertyName:0,elapsedTime:0,pseudoElement:0});m(u);r=k({},r,{deltaX:function(a){return"deltaX"in a?a.deltaX:"wheelDeltaX"in
a?-a.wheelDeltaX:0},deltaY:function(a){return"deltaY"in a?a.deltaY:"wheelDeltaY"in a?-a.wheelDeltaY:"wheelDelta"in a?-a.wheelDelta:0},deltaZ:0,deltaMode:0});m(r);var C=null,y=p.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Scheduler.unstable_flushAllWithoutAsserting;r=v.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Events;var Fa=r[5],P=r[6],Ga=v.unstable_batchedUpdates,Q=t.IsSomeRendererActing,ha="function"===typeof y,K=y||function(){for(var a=!1;Fa();)a=!0;return a},H=0,ia=!1,R=v.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Events[6],
Ha=v.unstable_batchedUpdates,S=t.IsSomeRendererActing,oa=function(a,b,c,d,e,g,f,h,k){var l=Array.prototype.slice.call(arguments,3);try{b.apply(c,l)}catch(Ia){this.onError(Ia)}},z=!1,D=null,E=!1,L=null,pa={onError:function(a){z=!0;D=a}};t=v.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Events;var ya=t[0],ta=t[1],ua=t[2],Ba=t[3],Ca=t[4],ja={},Aa=new Set(["mouseEnter","mouseLeave","pointerEnter","pointerLeave"]),Ja="blur cancel click close contextMenu copy cut auxClick doubleClick dragEnd dragStart drop focus input invalid keyDown keyPress keyUp mouseDown mouseUp paste pause play pointerCancel pointerDown pointerUp rateChange reset seeked submit touchCancel touchEnd touchStart volumeChange drag dragEnter dragExit dragLeave dragOver mouseMove mouseOut mouseOver pointerMove pointerOut pointerOver scroll toggle touchMove wheel abort animationEnd animationIteration animationStart canPlay canPlayThrough durationChange emptied encrypted ended error gotPointerCapture load loadedData loadedMetadata loadStart lostPointerCapture playing progress seeking stalled suspend timeUpdate transitionEnd waiting mouseEnter mouseLeave pointerEnter pointerLeave change select beforeInput compositionEnd compositionStart compositionUpdate".split(" ");
(function(){Ja.forEach(function(a){ja[a]=xa(a)})})();h.Simulate=ja;h.act=function(a){function b(){H--;Q.current=c;P.current=d}!1===ia&&(ia=!0,console.error("act(...) is not supported in production builds of React, and might not behave as expected."));H++;var c=Q.current,d=P.current;Q.current=!0;P.current=!0;try{var e=Ga(a)}catch(g){throw b(),g;}if(null!==e&&"object"===typeof e&&"function"===typeof e.then)return{then:function(a,d){e.then(function(){1<H||!0===ha&&!0===c?(b(),a()):X(function(c){b();
c?d(c):a()})},function(a){b();d(a)})}};try{1!==H||!1!==ha&&!1!==c||K(),b()}catch(g){throw b(),g;}return{then:function(a){a()}}};h.findAllInRenderedTree=G;h.findRenderedComponentWithType=function(a,b){w(a,"findRenderedComponentWithType");a=ca(a,b);if(1!==a.length)throw Error("Did not find exactly one match (found: "+a.length+") for componentType:"+b);return a[0]};h.findRenderedDOMComponentWithClass=function(a,b){w(a,"findRenderedDOMComponentWithClass");a=aa(a,b);if(1!==a.length)throw Error("Did not find exactly one match (found: "+
a.length+") for class:"+b);return a[0]};h.findRenderedDOMComponentWithTag=function(a,b){w(a,"findRenderedDOMComponentWithTag");a=ba(a,b);if(1!==a.length)throw Error("Did not find exactly one match (found: "+a.length+") for tag:"+b);return a[0]};h.isCompositeComponent=M;h.isCompositeComponentWithType=Z;h.isDOMComponent=F;h.isDOMComponentElement=function(a){return!!(a&&p.isValidElement(a)&&a.tagName)};h.isElement=function(a){return p.isValidElement(a)};h.isElementOfType=function(a,b){return p.isValidElement(a)&&
a.type===b};h.mockComponent=function(a,b){b=b||a.mockTagName||"div";a.prototype.render.mockImplementation(function(){return p.createElement(b,null,this.props.children)});return this};h.nativeTouchData=function(a,b){return{touches:[{pageX:a,pageY:b}]}};h.renderIntoDocument=function(a){var b=document.createElement("div");return v.render(a,b)};h.scryRenderedComponentsWithType=ca;h.scryRenderedDOMComponentsWithClass=aa;h.scryRenderedDOMComponentsWithTag=ba;h.traverseTwoPhase=ea;h.unstable_concurrentAct=
function(a){if(void 0===y)throw Error("This version of `act` requires a special mock build of Scheduler.");if(!0!==setTimeout._isMockFunction)throw Error("This version of `act` requires Jest's timer mocks (i.e. jest.useFakeTimers).");var b=S.current,c=R.current;S.current=!0;R.current=!0;var d=function(){S.current=b;R.current=c};try{var e=Ha(a);if("object"===typeof e&&null!==e&&"function"===typeof e.then)return{then:function(a,b){e.then(function(){Y(function(){d();a()},function(a){d();b(a)})},function(a){d();
b(a)})}};try{do var g=y();while(g)}finally{d()}}catch(f){throw d(),f;}}});
})();
