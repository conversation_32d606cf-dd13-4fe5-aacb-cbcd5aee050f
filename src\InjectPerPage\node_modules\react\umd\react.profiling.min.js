/** @license React v17.0.2
 * react.profiling.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
(function(){'use strict';(function(f,C){"object"===typeof exports&&"undefined"!==typeof module?C(exports):"function"===typeof define&&define.amd?define(["exports"],C):(f=f||self,C(f.React={}))})(this,function(f){function C(a){if(null===a||"object"!==typeof a)return null;a=da&&a[da]||a["@@iterator"];return"function"===typeof a?a:null}function D(a){for(var b="https://reactjs.org/docs/error-decoder.html?invariant="+a,c=1;c<arguments.length;c++)b+="&args[]="+encodeURIComponent(arguments[c]);return"Minified React error #"+
a+"; visit "+b+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function A(a,b,c){this.props=a;this.context=b;this.refs=ea;this.updater=c||fa}function ha(){}function P(a,b,c){this.props=a;this.context=b;this.refs=ea;this.updater=c||fa}function ia(a,b,c){var e,d={},l=null,h=null;if(null!=b)for(e in void 0!==b.ref&&(h=b.ref),void 0!==b.key&&(l=""+b.key),b)ja.call(b,e)&&!ka.hasOwnProperty(e)&&(d[e]=b[e]);var k=arguments.length-2;if(1===
k)d.children=c;else if(1<k){for(var f=Array(k),g=0;g<k;g++)f[g]=arguments[g+2];d.children=f}if(a&&a.defaultProps)for(e in k=a.defaultProps,k)void 0===d[e]&&(d[e]=k[e]);return{$$typeof:B,type:a,key:l,ref:h,props:d,_owner:Q.current}}function za(a,b){return{$$typeof:B,type:a.type,key:b,ref:a.ref,props:a.props,_owner:a._owner}}function R(a){return"object"===typeof a&&null!==a&&a.$$typeof===B}function Aa(a){var b={"=":"=0",":":"=2"};return"$"+a.replace(/[=:]/g,function(a){return b[a]})}function S(a,b){return"object"===
typeof a&&null!==a&&null!=a.key?Aa(""+a.key):b.toString(36)}function H(a,b,c,e,d){var l=typeof a;if("undefined"===l||"boolean"===l)a=null;var h=!1;if(null===a)h=!0;else switch(l){case "string":case "number":h=!0;break;case "object":switch(a.$$typeof){case B:case la:h=!0}}if(h)return h=a,d=d(h),a=""===e?"."+S(h,0):e,Array.isArray(d)?(c="",null!=a&&(c=a.replace(ma,"$&/")+"/"),H(d,b,c,"",function(a){return a})):null!=d&&(R(d)&&(d=za(d,c+(!d.key||h&&h.key===d.key?"":(""+d.key).replace(ma,"$&/")+"/")+
a)),b.push(d)),1;h=0;e=""===e?".":e+":";if(Array.isArray(a))for(var f=0;f<a.length;f++){l=a[f];var n=e+S(l,f);h+=H(l,b,c,n,d)}else if(n=C(a),"function"===typeof n)for(a=n.call(a),f=0;!(l=a.next()).done;)l=l.value,n=e+S(l,f++),h+=H(l,b,c,n,d);else if("object"===l)throw b=""+a,Error(D(31,"[object Object]"===b?"object with keys {"+Object.keys(a).join(", ")+"}":b));return h}function I(a,b,c){if(null==a)return a;var e=[],d=0;H(a,e,"","",function(a){return b.call(c,a,d++)});return e}function Ba(a){if(-1===
a._status){var b=a._result;b=b();a._status=0;a._result=b;b.then(function(b){0===a._status&&(b=b.default,a._status=1,a._result=b)},function(b){0===a._status&&(a._status=2,a._result=b)})}if(1===a._status)return a._result;throw a._result;}function v(){var a=na.current;if(null===a)throw Error(D(321));return a}function T(a,b){var c=a.length;a.push(b);a:for(;;){var e=c-1>>>1,d=a[e];if(void 0!==d&&0<J(d,b))a[e]=b,a[c]=d,c=e;else break a}}function r(a){a=a[0];return void 0===a?null:a}function K(a){var b=
a[0];if(void 0!==b){var c=a.pop();if(c!==b){a[0]=c;a:for(var e=0,d=a.length;e<d;){var f=2*(e+1)-1,h=a[f],k=f+1,n=a[k];if(void 0!==h&&0>J(h,c))void 0!==n&&0>J(n,h)?(a[e]=n,a[k]=c,e=k):(a[e]=h,a[f]=c,e=f);else if(void 0!==n&&0>J(n,c))a[e]=n,a[k]=c,e=k;else break a}}return b}return null}function J(a,b){var c=a.sortIndex-b.sortIndex;return 0!==c?c:a.id-b.id}function U(a){for(var b=r(w);null!==b;){if(null===b.callback)K(w);else if(b.startTime<=a)K(w),b.sortIndex=b.expirationTime,T(u,b);else break;b=r(w)}}
function V(a){E=!1;U(a);if(!z)if(null!==r(u))z=!0,F(W);else{var b=r(w);null!==b&&L(V,b.startTime-a)}}function W(a,b){z=!1;E&&(E=!1,X());M=!0;var c=m;try{U(b);for(q=r(u);null!==q&&(!(q.expirationTime>b)||a&&!Y());){var e=q.callback;if("function"===typeof e){q.callback=null;m=q.priorityLevel;var d=e(q.expirationTime<=b);b=x();"function"===typeof d?q.callback=d:q===r(u)&&K(u);U(b)}else K(u);q=r(u)}if(null!==q)var f=!0;else{var h=r(w);null!==h&&L(V,h.startTime-b);f=!1}return f}finally{q=null,m=c,M=!1}}
function Ca(a){var b=!1,c=null;t.forEach(function(e){try{e.onInteractionTraced(a)}catch(d){b||(b=!0,c=d)}});if(b)throw c;}function Da(a){var b=!1,c=null;t.forEach(function(e){try{e.onInteractionScheduledWorkCompleted(a)}catch(d){b||(b=!0,c=d)}});if(b)throw c;}function Ea(a,b){var c=!1,e=null;t.forEach(function(d){try{d.onWorkScheduled(a,b)}catch(l){c||(c=!0,e=l)}});if(c)throw e;}function Fa(a,b){var c=!1,e=null;t.forEach(function(d){try{d.onWorkStarted(a,b)}catch(l){c||(c=!0,e=l)}});if(c)throw e;
}function Ga(a,b){var c=!1,e=null;t.forEach(function(d){try{d.onWorkStopped(a,b)}catch(l){c||(c=!0,e=l)}});if(c)throw e;}function Ha(a,b){var c=!1,e=null;t.forEach(function(d){try{d.onWorkCanceled(a,b)}catch(l){c||(c=!0,e=l)}});if(c)throw e;}var B=60103,la=60106;f.Fragment=60107;f.StrictMode=60108;f.Profiler=60114;var oa=60109,pa=60110,qa=60112;f.Suspense=60113;var ra=60115,sa=60116;if("function"===typeof Symbol&&Symbol.for){var g=Symbol.for;B=g("react.element");la=g("react.portal");f.Fragment=g("react.fragment");
f.StrictMode=g("react.strict_mode");f.Profiler=g("react.profiler");oa=g("react.provider");pa=g("react.context");qa=g("react.forward_ref");f.Suspense=g("react.suspense");ra=g("react.memo");sa=g("react.lazy")}var da="function"===typeof Symbol&&Symbol.iterator,Ia=Object.prototype.hasOwnProperty,Z=Object.assign||function(a,b){if(null==a)throw new TypeError("Object.assign target cannot be null or undefined");for(var c=Object(a),e=1;e<arguments.length;e++){var d=arguments[e];if(null!=d){var f=void 0;d=
Object(d);for(f in d)Ia.call(d,f)&&(c[f]=d[f])}}return c},fa={isMounted:function(a){return!1},enqueueForceUpdate:function(a,b,c){},enqueueReplaceState:function(a,b,c,e){},enqueueSetState:function(a,b,c,e){}},ea={};A.prototype.isReactComponent={};A.prototype.setState=function(a,b){if("object"!==typeof a&&"function"!==typeof a&&null!=a)throw Error(D(85));this.updater.enqueueSetState(this,a,b,"setState")};A.prototype.forceUpdate=function(a){this.updater.enqueueForceUpdate(this,a,"forceUpdate")};ha.prototype=
A.prototype;g=P.prototype=new ha;g.constructor=P;Z(g,A.prototype);g.isPureReactComponent=!0;var Q={current:null},ja=Object.prototype.hasOwnProperty,ka={key:!0,ref:!0,__self:!0,__source:!0},ma=/\/+/g,na={current:null},aa;if("object"===typeof performance&&"function"===typeof performance.now){var Ja=performance;var x=function(){return Ja.now()}}else{var ta=Date,Ka=ta.now();x=function(){return ta.now()-Ka}}if("undefined"===typeof window||"function"!==typeof MessageChannel){var G=null,ua=null,va=function(){if(null!==
G)try{var a=x();G(!0,a);G=null}catch(b){throw setTimeout(va,0),b;}};var F=function(a){null!==G?setTimeout(F,0,a):(G=a,setTimeout(va,0))};var L=function(a,b){ua=setTimeout(a,b)};var X=function(){clearTimeout(ua)};var Y=function(){return!1};g=aa=function(){}}else{var La=window.setTimeout,Ma=window.clearTimeout;"undefined"!==typeof console&&(g=window.cancelAnimationFrame,"function"!==typeof window.requestAnimationFrame&&console.error("This browser doesn't support requestAnimationFrame. Make sure that you load a polyfill in older browsers. https://reactjs.org/link/react-polyfills"),
"function"!==typeof g&&console.error("This browser doesn't support cancelAnimationFrame. Make sure that you load a polyfill in older browsers. https://reactjs.org/link/react-polyfills"));var N=!1,O=null,ba=-1,wa=5,xa=0;Y=function(){return x()>=xa};g=function(){};aa=function(a){0>a||125<a?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):wa=0<a?Math.floor(1E3/a):5};var ya=new MessageChannel,ca=ya.port2;ya.port1.onmessage=
function(){if(null!==O){var a=x();xa=a+wa;try{O(!0,a)?ca.postMessage(null):(N=!1,O=null)}catch(b){throw ca.postMessage(null),b;}}else N=!1};F=function(a){O=a;N||(N=!0,ca.postMessage(null))};L=function(a,b){ba=La(function(){a(x())},b)};X=function(){Ma(ba);ba=-1}}var u=[],w=[],Na=1,q=null,m=3,M=!1,z=!1,E=!1;g={__proto__:null,unstable_ImmediatePriority:1,unstable_UserBlockingPriority:2,unstable_NormalPriority:3,unstable_IdlePriority:5,unstable_LowPriority:4,unstable_runWithPriority:function(a,b){switch(a){case 1:case 2:case 3:case 4:case 5:break;
default:a=3}var c=m;m=a;try{return b()}finally{m=c}},unstable_next:function(a){switch(m){case 1:case 2:case 3:var b=3;break;default:b=m}var c=m;m=b;try{return a()}finally{m=c}},unstable_scheduleCallback:function(a,b,c){var e=x();"object"===typeof c&&null!==c?(c=c.delay,c="number"===typeof c&&0<c?e+c:e):c=e;switch(a){case 1:var d=-1;break;case 2:d=250;break;case 5:d=1073741823;break;case 4:d=1E4;break;default:d=5E3}d=c+d;a={id:Na++,callback:b,priorityLevel:a,startTime:c,expirationTime:d,sortIndex:-1};
c>e?(a.sortIndex=c,T(w,a),null===r(u)&&a===r(w)&&(E?X():E=!0,L(V,c-e))):(a.sortIndex=d,T(u,a),z||M||(z=!0,F(W)));return a},unstable_cancelCallback:function(a){a.callback=null},unstable_wrapCallback:function(a){var b=m;return function(){var c=m;m=b;try{return a.apply(this,arguments)}finally{m=c}}},unstable_getCurrentPriorityLevel:function(){return m},get unstable_shouldYield(){return Y},unstable_requestPaint:g,unstable_continueExecution:function(){z||M||(z=!0,F(W))},unstable_pauseExecution:function(){},
unstable_getFirstCallbackNode:function(){return r(u)},get unstable_now(){return x},get unstable_forceFrameRate(){return aa},unstable_Profiling:null};var Oa=0,Pa=0,p=null,y=null;p={current:new Set};y={current:null};var t=null;t=new Set;g={ReactCurrentDispatcher:na,ReactCurrentOwner:Q,IsSomeRendererActing:{current:!1},ReactCurrentBatchConfig:{transition:0},assign:Z,Scheduler:g,SchedulerTracing:{__proto__:null,get __interactionsRef(){return p},get __subscriberRef(){return y},unstable_clear:function(a){var b=
p.current;p.current=new Set;try{return a()}finally{p.current=b}},unstable_getCurrent:function(){return p.current},unstable_getThreadID:function(){return++Pa},unstable_trace:function(a,b,c){var e=3<arguments.length&&void 0!==arguments[3]?arguments[3]:0,d={__count:1,id:Oa++,name:a,timestamp:b},f=p.current,h=new Set(f);h.add(d);p.current=h;var k=y.current;try{if(null!==k)k.onInteractionTraced(d)}finally{try{if(null!==k)k.onWorkStarted(h,e)}finally{try{var n=c()}finally{p.current=f;try{if(null!==k)k.onWorkStopped(h,
e)}finally{if(d.__count--,null!==k&&0===d.__count)k.onInteractionScheduledWorkCompleted(d)}}}}return n},unstable_wrap:function(a){function b(){var b=p.current;p.current=e;d=y.current;try{try{if(null!==d)d.onWorkStarted(e,c)}finally{try{var k=a.apply(void 0,arguments)}finally{if(p.current=b,null!==d)d.onWorkStopped(e,c)}}return k}finally{f||(f=!0,e.forEach(function(a){a.__count--;if(null!==d&&0===a.__count)d.onInteractionScheduledWorkCompleted(a)}))}}var c=1<arguments.length&&void 0!==arguments[1]?
arguments[1]:0,e=p.current,d=y.current;if(null!==d)d.onWorkScheduled(e,c);e.forEach(function(a){a.__count++});var f=!1;b.cancel=function(){d=y.current;try{if(null!==d)d.onWorkCanceled(e,c)}finally{e.forEach(function(a){a.__count--;if(d&&0===a.__count)d.onInteractionScheduledWorkCompleted(a)})}};return b},unstable_subscribe:function(a){t.add(a);1===t.size&&(y.current={onInteractionScheduledWorkCompleted:Da,onInteractionTraced:Ca,onWorkCanceled:Ha,onWorkScheduled:Ea,onWorkStarted:Fa,onWorkStopped:Ga})},
unstable_unsubscribe:function(a){t.delete(a);0===t.size&&(y.current=null)}}};f.Children={map:I,forEach:function(a,b,c){I(a,function(){b.apply(this,arguments)},c)},count:function(a){var b=0;I(a,function(){b++});return b},toArray:function(a){return I(a,function(a){return a})||[]},only:function(a){if(!R(a))throw Error(D(143));return a}};f.Component=A;f.PureComponent=P;f.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=g;f.cloneElement=function(a,b,c){if(null===a||void 0===a)throw Error(D(267,a));var e=
Z({},a.props),d=a.key,f=a.ref,h=a._owner;if(null!=b){void 0!==b.ref&&(f=b.ref,h=Q.current);void 0!==b.key&&(d=""+b.key);if(a.type&&a.type.defaultProps)var k=a.type.defaultProps;for(g in b)ja.call(b,g)&&!ka.hasOwnProperty(g)&&(e[g]=void 0===b[g]&&void 0!==k?k[g]:b[g])}var g=arguments.length-2;if(1===g)e.children=c;else if(1<g){k=Array(g);for(var m=0;m<g;m++)k[m]=arguments[m+2];e.children=k}return{$$typeof:B,type:a.type,key:d,ref:f,props:e,_owner:h}};f.createContext=function(a,b){void 0===b&&(b=null);
a={$$typeof:pa,_calculateChangedBits:b,_currentValue:a,_currentValue2:a,_threadCount:0,Provider:null,Consumer:null};a.Provider={$$typeof:oa,_context:a};return a.Consumer=a};f.createElement=ia;f.createFactory=function(a){var b=ia.bind(null,a);b.type=a;return b};f.createRef=function(){return{current:null}};f.forwardRef=function(a){return{$$typeof:qa,render:a}};f.isValidElement=R;f.lazy=function(a){return{$$typeof:sa,_payload:{_status:-1,_result:a},_init:Ba}};f.memo=function(a,b){return{$$typeof:ra,
type:a,compare:void 0===b?null:b}};f.useCallback=function(a,b){return v().useCallback(a,b)};f.useContext=function(a,b){return v().useContext(a,b)};f.useDebugValue=function(a,b){};f.useEffect=function(a,b){return v().useEffect(a,b)};f.useImperativeHandle=function(a,b,c){return v().useImperativeHandle(a,b,c)};f.useLayoutEffect=function(a,b){return v().useLayoutEffect(a,b)};f.useMemo=function(a,b){return v().useMemo(a,b)};f.useReducer=function(a,b,c){return v().useReducer(a,b,c)};f.useRef=function(a){return v().useRef(a)};
f.useState=function(a){return v().useState(a)};f.version="17.0.2"});
})();
