{"name": "script-injector", "version": "0.0.1", "private": true, "engines": {"node": ">=22.14.0 < 23.0.0"}, "main": "lib/index.js", "scripts": {"build": "gulp bundle", "clean": "gulp clean", "test": "gulp test"}, "dependencies": {"@microsoft/sp-component-base": "1.21.1", "@microsoft/sp-core-library": "1.21.1", "@microsoft/sp-lodash-subset": "1.21.1", "@microsoft/sp-office-ui-fabric-core": "1.21.1", "@microsoft/sp-property-pane": "^1.21.1", "@microsoft/sp-webpart-base": "1.21.1", "tslib": "2.3.1"}, "devDependencies": {"@fluentui/react": "^8.106.4", "@microsoft/eslint-config-spfx": "1.21.1", "@microsoft/eslint-plugin-spfx": "1.21.1", "@microsoft/rush-stack-compiler-5.3": "0.1.0", "@microsoft/sp-build-web": "1.21.1", "@microsoft/sp-module-interfaces": "1.21.1", "@rushstack/eslint-config": "4.0.1", "@types/webpack-env": "~1.15.2", "ajv": "^6.12.5", "eslint": "8.57.1", "gulp": "4.0.2", "typescript": "~5.3.3"}}