import { Version } from '@microsoft/sp-core-library';
import {
  type IPropertyPaneConfiguration,
  PropertyPaneTextField
} from '@microsoft/sp-property-pane';
import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import type { IReadonlyTheme } from '@microsoft/sp-component-base';
import { SPComponentLoader } from '@microsoft/sp-loader';
import { escape } from '@microsoft/sp-lodash-subset';

import styles from './InjectorWebPart.module.scss';
import * as strings from 'InjectorWebPartStrings';

export interface IInjectorWebPartProps {
  description: string;
  cssUrl?: string;   
  jsUrl?: string;  
}

export default class InjectorWebPart extends BaseClientSideWebPart<IInjectorWebPartProps> {

  private _environmentMessage: string = '';
  // private _isDarkTheme: boolean = false;

  /** Load CSS/JS as soon as the web part initializes */
  protected async onInit(): Promise<void> {
    await this._getEnvironmentMessage().then(m => this._environmentMessage = m);

    // Inject CSS first (non-blocking)
    if (this.properties?.cssUrl) {
      SPComponentLoader.loadCss(this.properties.cssUrl);
    }
    // Then inject JS (await so you can rely on it being present after init)
    if (this.properties?.jsUrl) {
      await SPComponentLoader.loadScript(this.properties.jsUrl);
    }
  }

  public render(): void {
    this.domElement.innerHTML = `
      <section class="${styles.injector} ${!!this.context.sdks.microsoftTeams ? styles.teams : ''}">
        <div class="${styles.welcome}">
          <h2>Hi, ${escape(this.context.pageContext.user.displayName)} 👋</h2>
          <div>${this._environmentMessage}</div>
          <div>CSS URL: <strong>${escape(this.properties.cssUrl || '(none)')}</strong></div>
          <div>JS URL: <strong>${escape(this.properties.jsUrl || '(none)')}</strong></div>
          <div>Note: URLs are configurable in the web part property pane.</div>
        </div>
      </section>`;
  }

  private _getEnvironmentMessage(): Promise<string> {
    if (!!this.context.sdks.microsoftTeams) {
      return this.context.sdks.microsoftTeams.teamsJs.app.getContext()
        .then(context => {
          let environmentMessage: string = '';
          switch (context.app.host.name) {
            case 'Office':
              environmentMessage = this.context.isServedFromLocalhost ? strings.AppLocalEnvironmentOffice : strings.AppOfficeEnvironment;
              break;
            case 'Outlook':
              environmentMessage = this.context.isServedFromLocalhost ? strings.AppLocalEnvironmentOutlook : strings.AppOutlookEnvironment;
              break;
            case 'Teams':
            case 'TeamsModern':
              environmentMessage = this.context.isServedFromLocalhost ? strings.AppLocalEnvironmentTeams : strings.AppTeamsTabEnvironment;
              break;
            default:
              environmentMessage = strings.UnknownEnvironment;
          }
          return environmentMessage;
        });
    }
    return Promise.resolve(this.context.isServedFromLocalhost ? strings.AppLocalEnvironmentSharePoint : strings.AppSharePointEnvironment);
  }

  protected onThemeChanged(currentTheme: IReadonlyTheme | undefined): void {
    if (!currentTheme) return;
    // this._isDarkTheme = !!currentTheme.isInverted;
    const { semanticColors } = currentTheme;
    if (semanticColors) {
      this.domElement.style.setProperty('--bodyText', semanticColors.bodyText || null);
      this.domElement.style.setProperty('--link', semanticColors.link || null);
      this.domElement.style.setProperty('--linkHovered', semanticColors.linkHovered || null);
    }
  }

  protected get dataVersion(): Version {
    return Version.parse('1.0');
  }

  /** Add fields for CSS/JS URLs so editors can configure per page */
  protected getPropertyPaneConfiguration(): IPropertyPaneConfiguration {
    return {
      pages: [
        {
          header: { description: strings.PropertyPaneDescription },
          groups: [
            {
              groupName: strings.BasicGroupName,
              groupFields: [
                PropertyPaneTextField('description', { label: strings.DescriptionFieldLabel }),
                PropertyPaneTextField('cssUrl', { label: 'CSS URL (optional)' }),
                PropertyPaneTextField('jsUrl',  { label: 'JS URL (optional)' })
              ]
            }
          ]
        }
      ]
    };
  }
}
